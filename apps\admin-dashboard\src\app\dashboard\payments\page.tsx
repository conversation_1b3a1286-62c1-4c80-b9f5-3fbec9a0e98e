'use client';

import { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  CurrencyDollarIcon,
  EyeIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';

interface Payment {
  id: string;
  bookingId: string;
  clientName: string;
  expertName: string;
  serviceName: string;
  amount: number;
  platformFee: number;
  expertEarnings: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'disputed';
  method: 'credit_card' | 'paypal' | 'bank_transfer' | 'wallet';
  transactionId: string;
  createdAt: string;
  processedAt?: string;
}

const mockPayments: Payment[] = [
  {
    id: '1',
    bookingId: 'BK001',
    clientName: 'سارة أحمد',
    expertName: 'أحمد محمد',
    serviceName: 'تصميم موقع إلكتروني',
    amount: 150,
    platformFee: 15,
    expertEarnings: 135,
    status: 'completed',
    method: 'credit_card',
    transactionId: 'TXN_001',
    createdAt: '2024-01-15',
    processedAt: '2024-01-15',
  },
  {
    id: '2',
    bookingId: 'BK002',
    clientName: 'محمد علي',
    expertName: 'فاطمة خالد',
    serviceName: 'استشارة تقنية',
    amount: 75,
    platformFee: 7.5,
    expertEarnings: 67.5,
    status: 'pending',
    method: 'paypal',
    transactionId: 'TXN_002',
    createdAt: '2024-01-18',
  },
  {
    id: '3',
    bookingId: 'BK003',
    clientName: 'نور حسن',
    expertName: 'خالد أحمد',
    serviceName: 'تطوير تطبيق موبايل',
    amount: 300,
    platformFee: 30,
    expertEarnings: 270,
    status: 'disputed',
    method: 'bank_transfer',
    transactionId: 'TXN_003',
    createdAt: '2024-01-10',
    processedAt: '2024-01-12',
  },
];

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>(mockPayments);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedMethod, setSelectedMethod] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.expertName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.transactionId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || payment.status === selectedStatus;
    const matchesMethod = selectedMethod === 'all' || payment.method === selectedMethod;
    
    return matchesSearch && matchesStatus && matchesMethod;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      refunded: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      disputed: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    };
    
    const labels = {
      pending: 'في الانتظار',
      completed: 'مكتمل',
      failed: 'فشل',
      refunded: 'مسترد',
      disputed: 'متنازع عليه',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  const getMethodBadge = (method: string) => {
    const labels = {
      credit_card: 'بطاقة ائتمان',
      paypal: 'PayPal',
      bank_transfer: 'تحويل بنكي',
      wallet: 'محفظة إلكترونية',
    };

    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
        {labels[method as keyof typeof labels]}
      </span>
    );
  };

  const handleRefundPayment = async (paymentId: string) => {
    if (window.confirm('هل أنت متأكد من استرداد هذا المبلغ؟')) {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setPayments(prev => prev.map(payment => 
          payment.id === paymentId ? { ...payment, status: 'refunded' as const } : payment
        ));
      } catch (error) {
        console.error('Error refunding payment:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const totalRevenue = payments.reduce((sum, payment) => 
    payment.status === 'completed' ? sum + payment.amount : sum, 0
  );
  
  const totalPlatformFees = payments.reduce((sum, payment) => 
    payment.status === 'completed' ? sum + payment.platformFee : sum, 0
  );

  const pendingPayments = payments.filter(p => p.status === 'pending').length;
  const disputedPayments = payments.filter(p => p.status === 'disputed').length;

  if (isLoading) {
    return <LoadingState message="جاري تحميل المدفوعات..." />;
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          إدارة المدفوعات
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          قائمة بجميع المدفوعات والمعاملات المالية في منصة فريلا سوريا
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الإيرادات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${totalRevenue.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    رسوم المنصة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${totalPlatformFees.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowPathIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    مدفوعات معلقة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {pendingPayments}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    نزاعات مالية
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {disputedPayments}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث في المدفوعات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* Status filter */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">في الانتظار</option>
              <option value="completed">مكتمل</option>
              <option value="failed">فشل</option>
              <option value="refunded">مسترد</option>
              <option value="disputed">متنازع عليه</option>
            </select>
          </div>

          {/* Method filter */}
          <div>
            <select
              value={selectedMethod}
              onChange={(e) => setSelectedMethod(e.target.value)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع طرق الدفع</option>
              <option value="credit_card">بطاقة ائتمان</option>
              <option value="paypal">PayPal</option>
              <option value="bank_transfer">تحويل بنكي</option>
              <option value="wallet">محفظة إلكترونية</option>
            </select>
          </div>
        </div>
      </div>

      {/* Payments table */}
      {filteredPayments.length === 0 ? (
        <EmptyState
          icon={<CurrencyDollarIcon className="h-12 w-12" />}
          title="لا توجد مدفوعات"
          description="لم يتم العثور على مدفوعات تطابق معايير البحث المحددة"
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    المعاملة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    المبالغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    طريقة الدفع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredPayments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {payment.serviceName}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          العميل: {payment.clientName}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          الخبير: {payment.expertName}
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          ID: {payment.transactionId}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        المبلغ الكلي: ${payment.amount}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        رسوم المنصة: ${payment.platformFee}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        أرباح الخبير: ${payment.expertEarnings}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getMethodBadge(payment.method)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(payment.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div>{payment.createdAt}</div>
                      {payment.processedAt && (
                        <div className="text-xs">معالج: {payment.processedAt}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <button 
                          type="button"
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400"
                          title="عرض التفاصيل"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        {payment.status === 'completed' && (
                          <button 
                            type="button"
                            onClick={() => handleRefundPayment(payment.id)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400"
                            title="استرداد المبلغ"
                          >
                            <ArrowPathIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState } from 'react';
import { 
  CameraIcon,
  CheckBadgeIcon,
  StarIcon,
  MapPinIcon,
  CalendarIcon,
  UserIcon,
  AcademicCapIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline';

interface ExpertProfile {
  id: string;
  name: string;
  title: string;
  bio: string;
  avatar?: string;
  email: string;
  phone: string;
  location: string;
  languages: string[];
  skills: string[];
  experience: number;
  education: string;
  certifications: string[];
  hourlyRate: number;
  availability: string;
  verified: boolean;
  rating: number;
  completedProjects: number;
  joinDate: string;
}

const mockProfile: ExpertProfile = {
  id: '1',
  name: 'أحمد محمد',
  title: 'مطور ويب متخصص في React و Node.js',
  bio: 'مطور ويب محترف مع أكثر من 5 سنوات من الخبرة في تطوير تطبيقات الويب الحديثة باستخدام React، Node.js، وقواعد البيانات المختلفة. أتخصص في بناء حلول تقنية مبتكرة وسهلة الاستخدام.',
  email: '<EMAIL>',
  phone: '+963 123 456 789',
  location: 'دمشق، سوريا',
  languages: ['العربية', 'الإنجليزية'],
  skills: ['React', 'Node.js', 'TypeScript', 'PostgreSQL', 'MongoDB', 'AWS'],
  experience: 5,
  education: 'بكالوريوس هندسة معلوماتية - جامعة دمشق',
  certifications: ['AWS Certified Developer', 'React Professional Certificate'],
  hourlyRate: 25,
  availability: 'متاح للعمل',
  verified: true,
  rating: 4.8,
  completedProjects: 47,
  joinDate: '2023-01-15',
};

export default function ProfilePage() {
  const [profile, setProfile] = useState<ExpertProfile>(mockProfile);
  const [isEditing, setIsEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState<ExpertProfile>(mockProfile);

  const handleSave = () => {
    setProfile(editedProfile);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            الملف الشخصي
          </h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            إدارة معلوماتك الشخصية ومهاراتك وخبراتك
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              تعديل الملف الشخصي
            </button>
          ) : (
            <div className="flex space-x-3 rtl:space-x-reverse">
              <button
                onClick={handleCancel}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600"
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                حفظ التغييرات
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            {/* Avatar */}
            <div className="text-center">
              <div className="relative inline-block">
                <div className="h-32 w-32 rounded-full bg-primary-500 flex items-center justify-center mx-auto">
                  <span className="text-4xl font-medium text-white">
                    {profile.name.charAt(0)}
                  </span>
                </div>
                {isEditing && (
                  <button className="absolute bottom-0 right-0 bg-white dark:bg-gray-700 rounded-full p-2 shadow-lg border border-gray-200 dark:border-gray-600">
                    <CameraIcon className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                  </button>
                )}
              </div>
              
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center justify-center">
                  {profile.name}
                  {profile.verified && (
                    <CheckBadgeIcon className="h-5 w-5 text-blue-500 mr-2" />
                  )}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {profile.title}
                </p>
              </div>

              {/* Stats */}
              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="flex items-center justify-center">
                    <StarIcon className="h-4 w-4 text-yellow-400" />
                    <span className="mr-1 text-lg font-semibold text-gray-900 dark:text-white">
                      {profile.rating}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">التقييم</p>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {profile.completedProjects}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">مشروع مكتمل</p>
                </div>
              </div>

              {/* Quick info */}
              <div className="mt-6 space-y-3 text-sm">
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <MapPinIcon className="h-4 w-4 ml-2" />
                  {profile.location}
                </div>
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <CalendarIcon className="h-4 w-4 ml-2" />
                  انضم في {profile.joinDate}
                </div>
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <BriefcaseIcon className="h-4 w-4 ml-2" />
                  {profile.experience} سنوات خبرة
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <UserIcon className="h-5 w-5 ml-2" />
              المعلومات الأساسية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الاسم الكامل
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedProfile.name}
                    onChange={(e) => setEditedProfile({...editedProfile, name: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{profile.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  البريد الإلكتروني
                </label>
                {isEditing ? (
                  <input
                    type="email"
                    value={editedProfile.email}
                    onChange={(e) => setEditedProfile({...editedProfile, email: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{profile.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  رقم الهاتف
                </label>
                {isEditing ? (
                  <input
                    type="tel"
                    value={editedProfile.phone}
                    onChange={(e) => setEditedProfile({...editedProfile, phone: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{profile.phone}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الموقع
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedProfile.location}
                    onChange={(e) => setEditedProfile({...editedProfile, location: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{profile.location}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  العنوان المهني
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedProfile.title}
                    onChange={(e) => setEditedProfile({...editedProfile, title: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{profile.title}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  نبذة عني
                </label>
                {isEditing ? (
                  <textarea
                    rows={4}
                    value={editedProfile.bio}
                    onChange={(e) => setEditedProfile({...editedProfile, bio: e.target.value})}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{profile.bio}</p>
                )}
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <AcademicCapIcon className="h-5 w-5 ml-2" />
              المعلومات المهنية
            </h3>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  المهارات
                </label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {profile.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  اللغات
                </label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {profile.languages.map((language, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                    >
                      {language}
                    </span>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    سنوات الخبرة
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {profile.experience} سنوات
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    السعر بالساعة
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    ${profile.hourlyRate}/ساعة
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  التعليم
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {profile.education}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  الشهادات
                </label>
                <ul className="mt-2 space-y-1">
                  {profile.certifications.map((cert, index) => (
                    <li key={index} className="text-sm text-gray-900 dark:text-white">
                      • {cert}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

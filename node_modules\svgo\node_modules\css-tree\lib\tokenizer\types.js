// CSS Syntax Module Level 3
// https://www.w3.org/TR/css-syntax-3/
export const EOF = 0;                 // <EOF-token>
export const Ident = 1;               // <ident-token>
export const Function = 2;            // <function-token>
export const AtKeyword = 3;           // <at-keyword-token>
export const Hash = 4;                // <hash-token>
export const String = 5;              // <string-token>
export const BadString = 6;           // <bad-string-token>
export const Url = 7;                 // <url-token>
export const BadUrl = 8;              // <bad-url-token>
export const Delim = 9;               // <delim-token>
export const Number = 10;             // <number-token>
export const Percentage = 11;         // <percentage-token>
export const Dimension = 12;          // <dimension-token>
export const WhiteSpace = 13;         // <whitespace-token>
export const CDO = 14;                // <CDO-token>
export const CDC = 15;                // <CDC-token>
export const Colon = 16;              // <colon-token>     :
export const Semicolon = 17;          // <semicolon-token> ;
export const Comma = 18;              // <comma-token>     ,
export const LeftSquareBracket = 19;  // <[-token>
export const RightSquareBracket = 20; // <]-token>
export const LeftParenthesis = 21;    // <(-token>
export const RightParenthesis = 22;   // <)-token>
export const LeftCurlyBracket = 23;   // <{-token>
export const RightCurlyBracket = 24;  // <}-token>
export const Comment = 25;

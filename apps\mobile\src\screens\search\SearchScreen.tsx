import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Image
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../contexts/ThemeContext';

interface Service {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: number;
  expertName: string;
  expertAvatar?: string;
  rating: number;
  reviewCount: number;
  category: string;
  tags: string[];
}

interface Category {
  id: string;
  name: string;
  icon: string;
  serviceCount: number;
}

const mockCategories: Category[] = [
  { id: '1', name: 'تطوير المواقع', icon: '💻', serviceCount: 45 },
  { id: '2', name: 'التصميم الجرافيكي', icon: '🎨', serviceCount: 32 },
  { id: '3', name: 'التسويق الرقمي', icon: '📱', serviceCount: 28 },
  { id: '4', name: 'الكتابة والترجمة', icon: '✍️', serviceCount: 21 },
  { id: '5', name: 'الاستشارات', icon: '💡', serviceCount: 18 },
  { id: '6', name: 'التدريب', icon: '📚', serviceCount: 15 },
];

const mockServices: Service[] = [
  {
    id: '1',
    title: 'تطوير موقع إلكتروني متكامل',
    description: 'تطوير موقع إلكتروني احترافي باستخدام أحدث التقنيات',
    price: 500,
    duration: 120,
    expertName: 'أحمد محمد',
    rating: 4.9,
    reviewCount: 23,
    category: 'تطوير المواقع',
    tags: ['React', 'Node.js', 'MongoDB'],
  },
  {
    id: '2',
    title: 'تصميم هوية بصرية كاملة',
    description: 'تصميم شعار وهوية بصرية متكاملة لشركتك',
    price: 200,
    duration: 60,
    expertName: 'فاطمة أحمد',
    rating: 4.8,
    reviewCount: 18,
    category: 'التصميم الجرافيكي',
    tags: ['Photoshop', 'Illustrator', 'Branding'],
  },
  {
    id: '3',
    title: 'استراتيجية تسويق رقمي',
    description: 'وضع خطة تسويقية شاملة لوسائل التواصل الاجتماعي',
    price: 150,
    duration: 90,
    expertName: 'محمد علي',
    rating: 4.7,
    reviewCount: 15,
    category: 'التسويق الرقمي',
    tags: ['Facebook', 'Instagram', 'Google Ads'],
  },
];

const SearchScreen: React.FC = () => {
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredServices, setFilteredServices] = useState(mockServices);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      setFilteredServices(mockServices);
    } else {
      const filtered = mockServices.filter(service =>
        service.title.toLowerCase().includes(query.toLowerCase()) ||
        service.description.toLowerCase().includes(query.toLowerCase()) ||
        service.expertName.toLowerCase().includes(query.toLowerCase()) ||
        service.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
      setFilteredServices(filtered);
    }
  };

  const handleCategorySelect = (categoryName: string) => {
    setSelectedCategory(categoryName);
    const filtered = mockServices.filter(service => service.category === categoryName);
    setFilteredServices(filtered);
  };

  const clearFilters = () => {
    setSelectedCategory(null);
    setSearchQuery('');
    setFilteredServices(mockServices);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Text key={i} style={{ color: i <= rating ? '#FFD700' : '#E5E5E5' }}>
          ⭐
        </Text>
      );
    }
    return stars;
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={[
        styles.categoryCard,
        {
          backgroundColor: selectedCategory === item.name ? colors.primary : colors.surface,
          borderColor: selectedCategory === item.name ? colors.primary : colors.border,
        }
      ]}
      onPress={() => handleCategorySelect(item.name)}
    >
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text style={[
        styles.categoryName,
        {
          color: selectedCategory === item.name ? colors.onPrimary : colors.text,
          fontFamily: 'Cairo-SemiBold'
        }
      ]}>
        {item.name}
      </Text>
      <Text style={[
        styles.serviceCount,
        { color: selectedCategory === item.name ? colors.onPrimary : colors.textSecondary }
      ]}>
        {item.serviceCount} خدمة
      </Text>
    </TouchableOpacity>
  );

  const renderServiceItem = ({ item }: { item: Service }) => (
    <TouchableOpacity style={[styles.serviceCard, { backgroundColor: colors.surface }]}>
      <View style={styles.serviceHeader}>
        <View style={styles.expertInfo}>
          <View style={[styles.expertAvatar, { backgroundColor: colors.primary }]}>
            <Text style={[styles.expertInitial, { color: colors.onPrimary }]}>
              {item.expertName.charAt(0)}
            </Text>
          </View>
          <View>
            <Text style={[styles.expertName, { color: colors.text }]}>
              {item.expertName}
            </Text>
            <View style={styles.ratingContainer}>
              <View style={styles.stars}>
                {renderStars(item.rating)}
              </View>
              <Text style={[styles.ratingText, { color: colors.textSecondary }]}>
                ({item.reviewCount})
              </Text>
            </View>
          </View>
        </View>
        <Text style={[styles.price, { color: colors.primary }]}>
          ${item.price}
        </Text>
      </View>

      <Text style={[styles.serviceTitle, { color: colors.text }]}>
        {item.title}
      </Text>

      <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
        {item.description}
      </Text>

      <View style={styles.serviceFooter}>
        <View style={styles.tags}>
          {item.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={[styles.tag, { backgroundColor: colors.primaryLight }]}>
              <Text style={[styles.tagText, { color: colors.primary }]}>
                {tag}
              </Text>
            </View>
          ))}
        </View>
        <Text style={[styles.duration, { color: colors.textSecondary }]}>
          {item.duration} دقيقة
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.title, { color: colors.text }]}>
          اكتشف الخدمات
        </Text>

        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: colors.background }]}>
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="ابحث عن خدمة أو خبير..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={handleSearch}
          />
          <Text style={styles.searchIcon}>🔍</Text>
        </View>

        {/* Clear Filters */}
        {(selectedCategory || searchQuery) && (
          <TouchableOpacity style={styles.clearButton} onPress={clearFilters}>
            <Text style={[styles.clearButtonText, { color: colors.primary }]}>
              مسح الفلاتر
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Categories */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            التصنيفات
          </Text>
          <FlatList
            data={mockCategories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Services */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {selectedCategory ? `خدمات ${selectedCategory}` : 'جميع الخدمات'}
          </Text>
          <FlatList
            data={filteredServices}
            renderItem={renderServiceItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            contentContainerStyle={styles.servicesList}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 12,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Cairo-Bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Cairo-Regular',
    textAlign: 'right',
  },
  searchIcon: {
    fontSize: 18,
    marginLeft: 8,
  },
  clearButton: {
    alignSelf: 'center',
    paddingVertical: 4,
  },
  clearButtonText: {
    fontSize: 14,
    fontFamily: 'Cairo-Medium',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Cairo-Bold',
    marginBottom: 12,
    marginHorizontal: 16,
  },
  categoriesList: {
    paddingHorizontal: 16,
  },
  categoryCard: {
    width: 120,
    padding: 16,
    borderRadius: 12,
    marginRight: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 4,
  },
  serviceCount: {
    fontSize: 12,
    textAlign: 'center',
  },
  servicesList: {
    paddingHorizontal: 16,
  },
  serviceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  expertInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  expertAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  expertInitial: {
    fontSize: 16,
    fontFamily: 'Cairo-Bold',
  },
  expertName: {
    fontSize: 14,
    fontFamily: 'Cairo-SemiBold',
    marginBottom: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stars: {
    flexDirection: 'row',
    marginRight: 4,
  },
  ratingText: {
    fontSize: 12,
    fontFamily: 'Cairo-Regular',
  },
  price: {
    fontSize: 18,
    fontFamily: 'Cairo-Bold',
  },
  serviceTitle: {
    fontSize: 16,
    fontFamily: 'Cairo-Bold',
    marginBottom: 8,
    textAlign: 'right',
  },
  serviceDescription: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
    lineHeight: 20,
    marginBottom: 12,
    textAlign: 'right',
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tags: {
    flexDirection: 'row',
    flex: 1,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 6,
  },
  tagText: {
    fontSize: 12,
    fontFamily: 'Cairo-Medium',
  },
  duration: {
    fontSize: 12,
    fontFamily: 'Cairo-Regular',
  },
});

export default SearchScreen;

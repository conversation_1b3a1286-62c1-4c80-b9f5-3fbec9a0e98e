var zl=Object.create;var Sr=Object.defineProperty;var Fl=Object.getOwnPropertyDescriptor;var Bl=Object.getOwnPropertyNames;var _l=Object.getPrototypeOf,Ul=Object.prototype.hasOwnProperty;var Ue=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),y=(e,t)=>{for(var r in t)Sr(e,r,{get:t[r],enumerable:!0})},jl=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Bl(t))!Ul.call(e,o)&&o!==r&&Sr(e,o,{get:()=>t[o],enumerable:!(n=Fl(t,o))||n.enumerable});return e};var ql=(e,t,r)=>(r=e!=null?zl(_l(e)):{},jl(t||!e||!e.__esModule?Sr(r,"default",{value:e,enumerable:!0}):r,e));var Zi=Ue(Dr=>{var Xi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");Dr.encode=function(e){if(0<=e&&e<Xi.length)return Xi[e];throw new TypeError("Must be between 0 and 63: "+e)};Dr.decode=function(e){var t=65,r=90,n=97,o=122,i=48,a=57,l=43,c=47,s=26,u=52;return t<=e&&e<=r?e-t:n<=e&&e<=o?e-n+s:i<=e&&e<=a?e-i+u:e==l?62:e==c?63:-1}});var na=Ue(Mr=>{var Ji=Zi(),Nr=5,ea=1<<Nr,ta=ea-1,ra=ea;function tc(e){return e<0?(-e<<1)+1:(e<<1)+0}function rc(e){var t=(e&1)===1,r=e>>1;return t?-r:r}Mr.encode=function(t){var r="",n,o=tc(t);do n=o&ta,o>>>=Nr,o>0&&(n|=ra),r+=Ji.encode(n);while(o>0);return r};Mr.decode=function(t,r,n){var o=t.length,i=0,a=0,l,c;do{if(r>=o)throw new Error("Expected more digits in base 64 VLQ value.");if(c=Ji.decode(t.charCodeAt(r++)),c===-1)throw new Error("Invalid base64 digit: "+t.charAt(r-1));l=!!(c&ra),c&=ta,i=i+(c<<a),a+=Nr}while(l);n.value=rc(i),n.rest=r}});var Gt=Ue(X=>{function nc(e,t,r){if(t in e)return e[t];if(arguments.length===3)return r;throw new Error('"'+t+'" is a required argument.')}X.getArg=nc;var oa=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,oc=/^data:.+\,.+$/;function dt(e){var t=e.match(oa);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}X.urlParse=dt;function Ve(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}X.urlGenerate=Ve;var ic=32;function ac(e){var t=[];return function(r){for(var n=0;n<t.length;n++)if(t[n].input===r){var o=t[0];return t[0]=t[n],t[n]=o,t[0].result}var i=e(r);return t.unshift({input:r,result:i}),t.length>ic&&t.pop(),i}}var Rr=ac(function(t){var r=t,n=dt(t);if(n){if(!n.path)return t;r=n.path}for(var o=X.isAbsolute(r),i=[],a=0,l=0;;)if(a=l,l=r.indexOf("/",a),l===-1){i.push(r.slice(a));break}else for(i.push(r.slice(a,l));l<r.length&&r[l]==="/";)l++;for(var c,s=0,l=i.length-1;l>=0;l--)c=i[l],c==="."?i.splice(l,1):c===".."?s++:s>0&&(c===""?(i.splice(l+1,s),s=0):(i.splice(l,2),s--));return r=i.join("/"),r===""&&(r=o?"/":"."),n?(n.path=r,Ve(n)):r});X.normalize=Rr;function ia(e,t){e===""&&(e="."),t===""&&(t=".");var r=dt(t),n=dt(e);if(n&&(e=n.path||"/"),r&&!r.scheme)return n&&(r.scheme=n.scheme),Ve(r);if(r||t.match(oc))return t;if(n&&!n.host&&!n.path)return n.host=t,Ve(n);var o=t.charAt(0)==="/"?t:Rr(e.replace(/\/+$/,"")+"/"+t);return n?(n.path=o,Ve(n)):o}X.join=ia;X.isAbsolute=function(e){return e.charAt(0)==="/"||oa.test(e)};function sc(e,t){e===""&&(e="."),e=e.replace(/\/$/,"");for(var r=0;t.indexOf(e+"/")!==0;){var n=e.lastIndexOf("/");if(n<0||(e=e.slice(0,n),e.match(/^([^\/]+:\/)?\/*$/)))return t;++r}return Array(r+1).join("../")+t.substr(e.length+1)}X.relative=sc;var aa=function(){var e=Object.create(null);return!("__proto__"in e)}();function sa(e){return e}function lc(e){return la(e)?"$"+e:e}X.toSetString=aa?sa:lc;function cc(e){return la(e)?e.slice(1):e}X.fromSetString=aa?sa:cc;function la(e){if(!e)return!1;var t=e.length;if(t<9||e.charCodeAt(t-1)!==95||e.charCodeAt(t-2)!==95||e.charCodeAt(t-3)!==111||e.charCodeAt(t-4)!==116||e.charCodeAt(t-5)!==111||e.charCodeAt(t-6)!==114||e.charCodeAt(t-7)!==112||e.charCodeAt(t-8)!==95||e.charCodeAt(t-9)!==95)return!1;for(var r=t-10;r>=0;r--)if(e.charCodeAt(r)!==36)return!1;return!0}function uc(e,t,r){var n=we(e.source,t.source);return n!==0||(n=e.originalLine-t.originalLine,n!==0)||(n=e.originalColumn-t.originalColumn,n!==0||r)||(n=e.generatedColumn-t.generatedColumn,n!==0)||(n=e.generatedLine-t.generatedLine,n!==0)?n:we(e.name,t.name)}X.compareByOriginalPositions=uc;function pc(e,t,r){var n;return n=e.originalLine-t.originalLine,n!==0||(n=e.originalColumn-t.originalColumn,n!==0||r)||(n=e.generatedColumn-t.generatedColumn,n!==0)||(n=e.generatedLine-t.generatedLine,n!==0)?n:we(e.name,t.name)}X.compareByOriginalPositionsNoSource=pc;function hc(e,t,r){var n=e.generatedLine-t.generatedLine;return n!==0||(n=e.generatedColumn-t.generatedColumn,n!==0||r)||(n=we(e.source,t.source),n!==0)||(n=e.originalLine-t.originalLine,n!==0)||(n=e.originalColumn-t.originalColumn,n!==0)?n:we(e.name,t.name)}X.compareByGeneratedPositionsDeflated=hc;function fc(e,t,r){var n=e.generatedColumn-t.generatedColumn;return n!==0||r||(n=we(e.source,t.source),n!==0)||(n=e.originalLine-t.originalLine,n!==0)||(n=e.originalColumn-t.originalColumn,n!==0)?n:we(e.name,t.name)}X.compareByGeneratedPositionsDeflatedNoLine=fc;function we(e,t){return e===t?0:e===null?1:t===null?-1:e>t?1:-1}function mc(e,t){var r=e.generatedLine-t.generatedLine;return r!==0||(r=e.generatedColumn-t.generatedColumn,r!==0)||(r=we(e.source,t.source),r!==0)||(r=e.originalLine-t.originalLine,r!==0)||(r=e.originalColumn-t.originalColumn,r!==0)?r:we(e.name,t.name)}X.compareByGeneratedPositionsInflated=mc;function dc(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))}X.parseSourceMapInput=dc;function gc(e,t,r){if(t=t||"",e&&(e[e.length-1]!=="/"&&t[0]!=="/"&&(e+="/"),t=e+t),r){var n=dt(r);if(!n)throw new Error("sourceMapURL could not be parsed");if(n.path){var o=n.path.lastIndexOf("/");o>=0&&(n.path=n.path.substring(0,o+1))}t=ia(Ve(n),t)}return Rr(t)}X.computeSourceURL=gc});var ua=Ue(ca=>{var zr=Gt(),Fr=Object.prototype.hasOwnProperty,ze=typeof Map<"u";function ve(){this._array=[],this._set=ze?new Map:Object.create(null)}ve.fromArray=function(t,r){for(var n=new ve,o=0,i=t.length;o<i;o++)n.add(t[o],r);return n};ve.prototype.size=function(){return ze?this._set.size:Object.getOwnPropertyNames(this._set).length};ve.prototype.add=function(t,r){var n=ze?t:zr.toSetString(t),o=ze?this.has(t):Fr.call(this._set,n),i=this._array.length;(!o||r)&&this._array.push(t),o||(ze?this._set.set(t,i):this._set[n]=i)};ve.prototype.has=function(t){if(ze)return this._set.has(t);var r=zr.toSetString(t);return Fr.call(this._set,r)};ve.prototype.indexOf=function(t){if(ze){var r=this._set.get(t);if(r>=0)return r}else{var n=zr.toSetString(t);if(Fr.call(this._set,n))return this._set[n]}throw new Error('"'+t+'" is not in the set.')};ve.prototype.at=function(t){if(t>=0&&t<this._array.length)return this._array[t];throw new Error("No element indexed by "+t)};ve.prototype.toArray=function(){return this._array.slice()};ca.ArraySet=ve});var fa=Ue(ha=>{var pa=Gt();function bc(e,t){var r=e.generatedLine,n=t.generatedLine,o=e.generatedColumn,i=t.generatedColumn;return n>r||n==r&&i>=o||pa.compareByGeneratedPositionsInflated(e,t)<=0}function Kt(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}Kt.prototype.unsortedForEach=function(t,r){this._array.forEach(t,r)};Kt.prototype.add=function(t){bc(this._last,t)?(this._last=t,this._array.push(t)):(this._sorted=!1,this._array.push(t))};Kt.prototype.toArray=function(){return this._sorted||(this._array.sort(pa.compareByGeneratedPositionsInflated),this._sorted=!0),this._array};ha.MappingList=Kt});var da=Ue(ma=>{var gt=na(),Y=Gt(),Qt=ua().ArraySet,yc=fa().MappingList;function se(e){e||(e={}),this._file=Y.getArg(e,"file",null),this._sourceRoot=Y.getArg(e,"sourceRoot",null),this._skipValidation=Y.getArg(e,"skipValidation",!1),this._sources=new Qt,this._names=new Qt,this._mappings=new yc,this._sourcesContents=null}se.prototype._version=3;se.fromSourceMap=function(t){var r=t.sourceRoot,n=new se({file:t.file,sourceRoot:r});return t.eachMapping(function(o){var i={generated:{line:o.generatedLine,column:o.generatedColumn}};o.source!=null&&(i.source=o.source,r!=null&&(i.source=Y.relative(r,i.source)),i.original={line:o.originalLine,column:o.originalColumn},o.name!=null&&(i.name=o.name)),n.addMapping(i)}),t.sources.forEach(function(o){var i=o;r!==null&&(i=Y.relative(r,o)),n._sources.has(i)||n._sources.add(i);var a=t.sourceContentFor(o);a!=null&&n.setSourceContent(o,a)}),n};se.prototype.addMapping=function(t){var r=Y.getArg(t,"generated"),n=Y.getArg(t,"original",null),o=Y.getArg(t,"source",null),i=Y.getArg(t,"name",null);this._skipValidation||this._validateMapping(r,n,o,i),o!=null&&(o=String(o),this._sources.has(o)||this._sources.add(o)),i!=null&&(i=String(i),this._names.has(i)||this._names.add(i)),this._mappings.add({generatedLine:r.line,generatedColumn:r.column,originalLine:n!=null&&n.line,originalColumn:n!=null&&n.column,source:o,name:i})};se.prototype.setSourceContent=function(t,r){var n=t;this._sourceRoot!=null&&(n=Y.relative(this._sourceRoot,n)),r!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[Y.toSetString(n)]=r):this._sourcesContents&&(delete this._sourcesContents[Y.toSetString(n)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))};se.prototype.applySourceMap=function(t,r,n){var o=r;if(r==null){if(t.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);o=t.file}var i=this._sourceRoot;i!=null&&(o=Y.relative(i,o));var a=new Qt,l=new Qt;this._mappings.unsortedForEach(function(c){if(c.source===o&&c.originalLine!=null){var s=t.originalPositionFor({line:c.originalLine,column:c.originalColumn});s.source!=null&&(c.source=s.source,n!=null&&(c.source=Y.join(n,c.source)),i!=null&&(c.source=Y.relative(i,c.source)),c.originalLine=s.line,c.originalColumn=s.column,s.name!=null&&(c.name=s.name))}var u=c.source;u!=null&&!a.has(u)&&a.add(u);var p=c.name;p!=null&&!l.has(p)&&l.add(p)},this),this._sources=a,this._names=l,t.sources.forEach(function(c){var s=t.sourceContentFor(c);s!=null&&(n!=null&&(c=Y.join(n,c)),i!=null&&(c=Y.relative(i,c)),this.setSourceContent(c,s))},this)};se.prototype._validateMapping=function(t,r,n,o){if(r&&typeof r.line!="number"&&typeof r.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if(!(t&&"line"in t&&"column"in t&&t.line>0&&t.column>=0&&!r&&!n&&!o)){if(t&&"line"in t&&"column"in t&&r&&"line"in r&&"column"in r&&t.line>0&&t.column>=0&&r.line>0&&r.column>=0&&n)return;throw new Error("Invalid mapping: "+JSON.stringify({generated:t,source:n,original:r,name:o}))}};se.prototype._serializeMappings=function(){for(var t=0,r=1,n=0,o=0,i=0,a=0,l="",c,s,u,p,h=this._mappings.toArray(),m=0,b=h.length;m<b;m++){if(s=h[m],c="",s.generatedLine!==r)for(t=0;s.generatedLine!==r;)c+=";",r++;else if(m>0){if(!Y.compareByGeneratedPositionsInflated(s,h[m-1]))continue;c+=","}c+=gt.encode(s.generatedColumn-t),t=s.generatedColumn,s.source!=null&&(p=this._sources.indexOf(s.source),c+=gt.encode(p-a),a=p,c+=gt.encode(s.originalLine-1-o),o=s.originalLine-1,c+=gt.encode(s.originalColumn-n),n=s.originalColumn,s.name!=null&&(u=this._names.indexOf(s.name),c+=gt.encode(u-i),i=u)),l+=c}return l};se.prototype._generateSourcesContent=function(t,r){return t.map(function(n){if(!this._sourcesContents)return null;r!=null&&(n=Y.relative(r,n));var o=Y.toSetString(n);return Object.prototype.hasOwnProperty.call(this._sourcesContents,o)?this._sourcesContents[o]:null},this)};se.prototype.toJSON=function(){var t={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(t.file=this._file),this._sourceRoot!=null&&(t.sourceRoot=this._sourceRoot),this._sourcesContents&&(t.sourcesContent=this._generateSourcesContent(t.sources,t.sourceRoot)),t};se.prototype.toString=function(){return JSON.stringify(this.toJSON())};ma.SourceMapGenerator=se});var Hl="5.0.4";var Fi={};y(Fi,{compress:()=>zi,find:()=>ld,findAll:()=>ud,findLast:()=>cd,fromPlainObject:()=>pd,generate:()=>ad,lexer:()=>nd,parse:()=>id,specificity:()=>kr,toPlainObject:()=>hd,tokenize:()=>od,walk:()=>sd});var Ee={};y(Ee,{AtKeyword:()=>M,BadString:()=>Ne,BadUrl:()=>K,CDC:()=>W,CDO:()=>pe,Colon:()=>R,Comma:()=>Q,Comment:()=>I,Delim:()=>g,Dimension:()=>k,EOF:()=>st,Function:()=>x,Hash:()=>A,Ident:()=>f,LeftCurlyBracket:()=>_,LeftParenthesis:()=>P,LeftSquareBracket:()=>H,Number:()=>d,Percentage:()=>L,RightCurlyBracket:()=>G,RightParenthesis:()=>S,RightSquareBracket:()=>$,Semicolon:()=>q,String:()=>V,Url:()=>U,WhiteSpace:()=>v});var st=0,f=1,x=2,M=3,A=4,V=5,Ne=6,U=7,K=8,g=9,d=10,L=11,k=12,v=13,pe=14,W=15,R=16,q=17,Q=18,H=19,$=20,P=21,S=22,_=23,G=24,I=25;function j(e){return e>=48&&e<=57}function oe(e){return j(e)||e>=65&&e<=70||e>=97&&e<=102}function _t(e){return e>=65&&e<=90}function Wl(e){return e>=97&&e<=122}function Yl(e){return _t(e)||Wl(e)}function Vl(e){return e>=128}function Bt(e){return Yl(e)||Vl(e)||e===95}function lt(e){return Bt(e)||j(e)||e===45}function Gl(e){return e>=0&&e<=8||e===11||e>=14&&e<=31||e===127}function ct(e){return e===10||e===13||e===12}function he(e){return ct(e)||e===32||e===9}function re(e,t){return!(e!==92||ct(t)||t===0)}function je(e,t,r){return e===45?Bt(t)||t===45||re(t,r):Bt(e)?!0:e===92?re(e,t):!1}function Ut(e,t,r){return e===43||e===45?j(t)?2:t===46&&j(r)?3:0:e===46?j(t)?2:0:j(e)?1:0}function jt(e){return e===65279||e===65534?1:0}var Cr=new Array(128),Kl=128,ut=130,Ar=131,qt=132,Er=133;for(let e=0;e<Cr.length;e++)Cr[e]=he(e)&&ut||j(e)&&Ar||Bt(e)&&qt||Gl(e)&&Er||e||Kl;function Ht(e){return e<128?Cr[e]:qt}function qe(e,t){return t<e.length?e.charCodeAt(t):0}function Wt(e,t,r){return r===13&&qe(e,t+1)===10?2:1}function xe(e,t,r){let n=e.charCodeAt(t);return _t(n)&&(n=n|32),n===r}function ke(e,t,r,n){if(r-t!==n.length||t<0||r>e.length)return!1;for(let o=t;o<r;o++){let i=n.charCodeAt(o-t),a=e.charCodeAt(o);if(_t(a)&&(a=a|32),a!==i)return!1}return!0}function _i(e,t){for(;t>=0&&he(e.charCodeAt(t));t--);return t+1}function pt(e,t){for(;t<e.length&&he(e.charCodeAt(t));t++);return t}function Tr(e,t){for(;t<e.length&&j(e.charCodeAt(t));t++);return t}function fe(e,t){if(t+=2,oe(qe(e,t-1))){for(let n=Math.min(e.length,t+5);t<n&&oe(qe(e,t));t++);let r=qe(e,t);he(r)&&(t+=Wt(e,t,r))}return t}function ht(e,t){for(;t<e.length;t++){let r=e.charCodeAt(t);if(!lt(r)){if(re(r,qe(e,t+1))){t=fe(e,t)-1;continue}break}}return t}function Me(e,t){let r=e.charCodeAt(t);if((r===43||r===45)&&(r=e.charCodeAt(t+=1)),j(r)&&(t=Tr(e,t+1),r=e.charCodeAt(t)),r===46&&j(e.charCodeAt(t+1))&&(t+=2,t=Tr(e,t)),xe(e,t,101)){let n=0;r=e.charCodeAt(t+1),(r===45||r===43)&&(n=1,r=e.charCodeAt(t+2)),j(r)&&(t=Tr(e,t+1+n+1))}return t}function Yt(e,t){for(;t<e.length;t++){let r=e.charCodeAt(t);if(r===41){t++;break}re(r,qe(e,t+1))&&(t=fe(e,t))}return t}function ft(e){if(e.length===1&&!oe(e.charCodeAt(0)))return e[0];let t=parseInt(e,16);return(t===0||t>=55296&&t<=57343||t>1114111)&&(t=65533),String.fromCodePoint(t)}var He=["EOF-token","ident-token","function-token","at-keyword-token","hash-token","string-token","bad-string-token","url-token","bad-url-token","delim-token","number-token","percentage-token","dimension-token","whitespace-token","CDO-token","CDC-token","colon-token","semicolon-token","comma-token","[-token","]-token","(-token",")-token","{-token","}-token"];function We(e=null,t){return e===null||e.length<t?new Uint32Array(Math.max(t+1024,16384)):e}var Ui=10,Ql=12,ji=13;function qi(e){let t=e.source,r=t.length,n=t.length>0?jt(t.charCodeAt(0)):0,o=We(e.lines,r),i=We(e.columns,r),a=e.startLine,l=e.startColumn;for(let c=n;c<r;c++){let s=t.charCodeAt(c);o[c]=a,i[c]=l++,(s===Ui||s===ji||s===Ql)&&(s===ji&&c+1<r&&t.charCodeAt(c+1)===Ui&&(c++,o[c]=a,i[c]=l),a++,l=1)}o[r]=a,i[r]=l,e.lines=o,e.columns=i,e.computed=!0}var Vt=class{constructor(){this.lines=null,this.columns=null,this.computed=!1}setSource(t,r=0,n=1,o=1){this.source=t,this.startOffset=r,this.startLine=n,this.startColumn=o,this.computed=!1}getLocation(t,r){return this.computed||qi(this),{source:r,offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}getLocationRange(t,r,n){return this.computed||qi(this),{source:n,start:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]},end:{offset:this.startOffset+r,line:this.lines[r],column:this.columns[r]}}}};var ae=16777215,Te=24,$l=new Map([[2,22],[21,22],[19,20],[23,24]]),mt=class{constructor(t,r){this.setSource(t,r)}reset(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset}setSource(t="",r=()=>{}){t=String(t||"");let n=t.length,o=We(this.offsetAndType,t.length+1),i=We(this.balance,t.length+1),a=0,l=0,c=0,s=-1;for(this.offsetAndType=null,this.balance=null,r(t,(u,p,h)=>{switch(u){default:i[a]=n;break;case l:{let m=c&ae;for(c=i[m],l=c>>Te,i[a]=m,i[m++]=a;m<a;m++)i[m]===n&&(i[m]=a);break}case 21:case 2:case 19:case 23:i[a]=c,l=$l.get(u),c=l<<Te|a;break}o[a++]=u<<Te|h,s===-1&&(s=p)}),o[a]=0<<Te|n,i[a]=n,i[n]=n;c!==0;){let u=c&ae;c=i[u],i[u]=n}this.source=t,this.firstCharOffset=s===-1?0:s,this.tokenCount=a,this.offsetAndType=o,this.balance=i,this.reset(),this.next()}lookupType(t){return t+=this.tokenIndex,t<this.tokenCount?this.offsetAndType[t]>>Te:0}lookupOffset(t){return t+=this.tokenIndex,t<this.tokenCount?this.offsetAndType[t-1]&ae:this.source.length}lookupValue(t,r){return t+=this.tokenIndex,t<this.tokenCount?ke(this.source,this.offsetAndType[t-1]&ae,this.offsetAndType[t]&ae,r):!1}getTokenStart(t){return t===this.tokenIndex?this.tokenStart:t>0?t<this.tokenCount?this.offsetAndType[t-1]&ae:this.offsetAndType[this.tokenCount]&ae:this.firstCharOffset}substrToCursor(t){return this.source.substring(t,this.tokenStart)}isBalanceEdge(t){return this.balance[this.tokenIndex]<t}isDelim(t,r){return r?this.lookupType(r)===9&&this.source.charCodeAt(this.lookupOffset(r))===t:this.tokenType===9&&this.source.charCodeAt(this.tokenStart)===t}skip(t){let r=this.tokenIndex+t;r<this.tokenCount?(this.tokenIndex=r,this.tokenStart=this.offsetAndType[r-1]&ae,r=this.offsetAndType[r],this.tokenType=r>>Te,this.tokenEnd=r&ae):(this.tokenIndex=this.tokenCount,this.next())}next(){let t=this.tokenIndex+1;t<this.tokenCount?(this.tokenIndex=t,this.tokenStart=this.tokenEnd,t=this.offsetAndType[t],this.tokenType=t>>Te,this.tokenEnd=t&ae):(this.eof=!0,this.tokenIndex=this.tokenCount,this.tokenType=0,this.tokenStart=this.tokenEnd=this.source.length)}skipSC(){for(;this.tokenType===13||this.tokenType===25;)this.next()}skipUntilBalanced(t,r){let n=t,o,i;e:for(;n<this.tokenCount;n++){if(o=this.balance[n],o<t)break e;switch(i=n>0?this.offsetAndType[n-1]&ae:this.firstCharOffset,r(this.source.charCodeAt(i))){case 1:break e;case 2:n++;break e;default:this.balance[o]===n&&(n=o)}}this.skip(n-this.tokenIndex)}forEachToken(t){for(let r=0,n=this.firstCharOffset;r<this.tokenCount;r++){let o=n,i=this.offsetAndType[r],a=i&ae,l=i>>Te;n=a,t(l,o,a,r)}}dump(){let t=new Array(this.tokenCount);return this.forEachToken((r,n,o,i)=>{t[i]={idx:i,type:He[r],chunk:this.source.substring(n,o),balance:this.balance[i]}}),t}};function Le(e,t){function r(p){return p<l?e.charCodeAt(p):0}function n(){if(s=Me(e,s),je(r(s),r(s+1),r(s+2))){u=12,s=ht(e,s);return}if(r(s)===37){u=11,s++;return}u=10}function o(){let p=s;if(s=ht(e,s),ke(e,p,s,"url")&&r(s)===40){if(s=pt(e,s+1),r(s)===34||r(s)===39){u=2,s=p+4;return}a();return}if(r(s)===40){u=2,s++;return}u=1}function i(p){for(p||(p=r(s++)),u=5;s<e.length;s++){let h=e.charCodeAt(s);switch(Ht(h)){case p:s++;return;case ut:if(ct(h)){s+=Wt(e,s,h),u=6;return}break;case 92:if(s===e.length-1)break;let m=r(s+1);ct(m)?s+=Wt(e,s+1,m):re(h,m)&&(s=fe(e,s)-1);break}}}function a(){for(u=7,s=pt(e,s);s<e.length;s++){let p=e.charCodeAt(s);switch(Ht(p)){case 41:s++;return;case ut:if(s=pt(e,s),r(s)===41||s>=e.length){s<e.length&&s++;return}s=Yt(e,s),u=8;return;case 34:case 39:case 40:case Er:s=Yt(e,s),u=8;return;case 92:if(re(p,r(s+1))){s=fe(e,s)-1;break}s=Yt(e,s),u=8;return}}}e=String(e||"");let l=e.length,c=jt(r(0)),s=c,u;for(;s<l;){let p=e.charCodeAt(s);switch(Ht(p)){case ut:u=13,s=pt(e,s+1);break;case 34:i();break;case 35:lt(r(s+1))||re(r(s+1),r(s+2))?(u=4,s=ht(e,s+1)):(u=9,s++);break;case 39:i();break;case 40:u=21,s++;break;case 41:u=22,s++;break;case 43:Ut(p,r(s+1),r(s+2))?n():(u=9,s++);break;case 44:u=18,s++;break;case 45:Ut(p,r(s+1),r(s+2))?n():r(s+1)===45&&r(s+2)===62?(u=15,s=s+3):je(p,r(s+1),r(s+2))?o():(u=9,s++);break;case 46:Ut(p,r(s+1),r(s+2))?n():(u=9,s++);break;case 47:r(s+1)===42?(u=25,s=e.indexOf("*/",s+2),s=s===-1?e.length:s+2):(u=9,s++);break;case 58:u=16,s++;break;case 59:u=17,s++;break;case 60:r(s+1)===33&&r(s+2)===45&&r(s+3)===45?(u=14,s=s+4):(u=9,s++);break;case 64:je(r(s+1),r(s+2),r(s+3))?(u=3,s=ht(e,s+1)):(u=9,s++);break;case 91:u=19,s++;break;case 92:re(p,r(s+1))?o():(u=9,s++);break;case 93:u=20,s++;break;case 123:u=23,s++;break;case 125:u=24,s++;break;case Ar:n();break;case qt:o();break;default:u=9,s++}t(u,c,c=s)}}var Ye=null,w=class{static createItem(t){return{prev:null,next:null,data:t}}constructor(){this.head=null,this.tail=null,this.cursor=null}createItem(t){return w.createItem(t)}allocateCursor(t,r){let n;return Ye!==null?(n=Ye,Ye=Ye.cursor,n.prev=t,n.next=r,n.cursor=this.cursor):n={prev:t,next:r,cursor:this.cursor},this.cursor=n,n}releaseCursor(){let{cursor:t}=this;this.cursor=t.cursor,t.prev=null,t.next=null,t.cursor=Ye,Ye=t}updateCursors(t,r,n,o){let{cursor:i}=this;for(;i!==null;)i.prev===t&&(i.prev=r),i.next===n&&(i.next=o),i=i.cursor}*[Symbol.iterator](){for(let t=this.head;t!==null;t=t.next)yield t.data}get size(){let t=0;for(let r=this.head;r!==null;r=r.next)t++;return t}get isEmpty(){return this.head===null}get first(){return this.head&&this.head.data}get last(){return this.tail&&this.tail.data}fromArray(t){let r=null;this.head=null;for(let n of t){let o=w.createItem(n);r!==null?r.next=o:this.head=o,o.prev=r,r=o}return this.tail=r,this}toArray(){return[...this]}toJSON(){return[...this]}forEach(t,r=this){let n=this.allocateCursor(null,this.head);for(;n.next!==null;){let o=n.next;n.next=o.next,t.call(r,o.data,o,this)}this.releaseCursor()}forEachRight(t,r=this){let n=this.allocateCursor(this.tail,null);for(;n.prev!==null;){let o=n.prev;n.prev=o.prev,t.call(r,o.data,o,this)}this.releaseCursor()}reduce(t,r,n=this){let o=this.allocateCursor(null,this.head),i=r,a;for(;o.next!==null;)a=o.next,o.next=a.next,i=t.call(n,i,a.data,a,this);return this.releaseCursor(),i}reduceRight(t,r,n=this){let o=this.allocateCursor(this.tail,null),i=r,a;for(;o.prev!==null;)a=o.prev,o.prev=a.prev,i=t.call(n,i,a.data,a,this);return this.releaseCursor(),i}some(t,r=this){for(let n=this.head;n!==null;n=n.next)if(t.call(r,n.data,n,this))return!0;return!1}map(t,r=this){let n=new w;for(let o=this.head;o!==null;o=o.next)n.appendData(t.call(r,o.data,o,this));return n}filter(t,r=this){let n=new w;for(let o=this.head;o!==null;o=o.next)t.call(r,o.data,o,this)&&n.appendData(o.data);return n}nextUntil(t,r,n=this){if(t===null)return;let o=this.allocateCursor(null,t);for(;o.next!==null;){let i=o.next;if(o.next=i.next,r.call(n,i.data,i,this))break}this.releaseCursor()}prevUntil(t,r,n=this){if(t===null)return;let o=this.allocateCursor(t,null);for(;o.prev!==null;){let i=o.prev;if(o.prev=i.prev,r.call(n,i.data,i,this))break}this.releaseCursor()}clear(){this.head=null,this.tail=null}copy(){let t=new w;for(let r of this)t.appendData(r);return t}prepend(t){return this.updateCursors(null,t,this.head,t),this.head!==null?(this.head.prev=t,t.next=this.head):this.tail=t,this.head=t,this}prependData(t){return this.prepend(w.createItem(t))}append(t){return this.insert(t)}appendData(t){return this.insert(w.createItem(t))}insert(t,r=null){if(r!==null)if(this.updateCursors(r.prev,t,r,t),r.prev===null){if(this.head!==r)throw new Error("before doesn't belong to list");this.head=t,r.prev=t,t.next=r,this.updateCursors(null,t)}else r.prev.next=t,t.prev=r.prev,r.prev=t,t.next=r;else this.updateCursors(this.tail,t,null,t),this.tail!==null?(this.tail.next=t,t.prev=this.tail):this.head=t,this.tail=t;return this}insertData(t,r){return this.insert(w.createItem(t),r)}remove(t){if(this.updateCursors(t,t.prev,t,t.next),t.prev!==null)t.prev.next=t.next;else{if(this.head!==t)throw new Error("item doesn't belong to list");this.head=t.next}if(t.next!==null)t.next.prev=t.prev;else{if(this.tail!==t)throw new Error("item doesn't belong to list");this.tail=t.prev}return t.prev=null,t.next=null,t}push(t){this.insert(w.createItem(t))}pop(){return this.tail!==null?this.remove(this.tail):null}unshift(t){this.prepend(w.createItem(t))}shift(){return this.head!==null?this.remove(this.head):null}prependList(t){return this.insertList(t,this.head)}appendList(t){return this.insertList(t)}insertList(t,r){return t.head===null?this:(r!=null?(this.updateCursors(r.prev,t.tail,r,t.head),r.prev!==null?(r.prev.next=t.head,t.head.prev=r.prev):this.head=t.head,r.prev=t.tail,t.tail.next=r):(this.updateCursors(this.tail,t.tail,null,t.head),this.tail!==null?(this.tail.next=t.head,t.head.prev=this.tail):this.head=t.head,this.tail=t.tail),t.head=null,t.tail=null,this)}replace(t,r){"head"in r?this.insertList(r,t):this.insert(r,t),this.remove(t)}};function Re(e,t){let r=Object.create(SyntaxError.prototype),n=new Error;return Object.assign(r,{name:e,message:t,get stack(){return(n.stack||"").replace(/^(.+\n){1,3}/,`${e}: ${t}
`)}})}var Lr=100,Hi=60,Wi="    ";function Yi({source:e,line:t,column:r},n){function o(u,p){return i.slice(u,p).map((h,m)=>String(u+m+1).padStart(c)+" |"+h).join(`
`)}let i=e.split(/\r\n?|\n|\f/),a=Math.max(1,t-n)-1,l=Math.min(t+n,i.length+1),c=Math.max(4,String(l).length)+1,s=0;r+=(Wi.length-1)*(i[t-1].substr(0,r-1).match(/\t/g)||[]).length,r>Lr&&(s=r-Hi+3,r=Hi-2);for(let u=a;u<=l;u++)u>=0&&u<i.length&&(i[u]=i[u].replace(/\t/g,Wi),i[u]=(s>0&&i[u].length>s?"\u2026":"")+i[u].substr(s,Lr-2)+(i[u].length>s+Lr-1?"\u2026":""));return[o(a,t),new Array(r+c+2).join("-")+"^",o(t,l)].filter(Boolean).join(`
`)}function Pr(e,t,r,n,o){return Object.assign(Re("SyntaxError",e),{source:t,offset:r,line:n,column:o,sourceFragment(a){return Yi({source:t,line:n,column:o},isNaN(a)?0:a)},get formattedMessage(){return`Parse error: ${e}
`+Yi({source:t,line:n,column:o},2)}})}function Vi(e){let t=this.createList(),r=!1,n={recognizer:e};for(;!this.eof;){switch(this.tokenType){case 25:this.next();continue;case 13:r=!0,this.next();continue}let o=e.getNode.call(this,n);if(o===void 0)break;r&&(e.onWhiteSpace&&e.onWhiteSpace.call(this,o,t,n),r=!1),t.push(o)}return r&&e.onWhiteSpace&&e.onWhiteSpace.call(this,null,t,n),t}var Gi=()=>{},Xl=33,Zl=35,Ir=59,Ki=123,Qi=0;function Jl(e){return function(){return this[e]()}}function Or(e){let t=Object.create(null);for(let r in e){let n=e[r],o=n.parse||n;o&&(t[r]=o)}return t}function ec(e){let t={context:Object.create(null),scope:Object.assign(Object.create(null),e.scope),atrule:Or(e.atrule),pseudo:Or(e.pseudo),node:Or(e.node)};for(let r in e.parseContext)switch(typeof e.parseContext[r]){case"function":t.context[r]=e.parseContext[r];break;case"string":t.context[r]=Jl(e.parseContext[r]);break}return{config:t,...t,...t.node}}function $i(e){let t="",r="<unknown>",n=!1,o=Gi,i=!1,a=new Vt,l=Object.assign(new mt,ec(e||{}),{parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:Vi,consumeUntilBalanceEnd:()=>0,consumeUntilLeftCurlyBracket(s){return s===Ki?1:0},consumeUntilLeftCurlyBracketOrSemicolon(s){return s===Ki||s===Ir?1:0},consumeUntilExclamationMarkOrSemicolon(s){return s===Xl||s===Ir?1:0},consumeUntilSemicolonIncluded(s){return s===Ir?2:0},createList(){return new w},createSingleNodeList(s){return new w().appendData(s)},getFirstListNode(s){return s&&s.first},getLastListNode(s){return s&&s.last},parseWithFallback(s,u){let p=this.tokenIndex;try{return s.call(this)}catch(h){if(i)throw h;let m=u.call(this,p);return i=!0,o(h,m),i=!1,m}},lookupNonWSType(s){let u;do if(u=this.lookupType(s++),u!==13)return u;while(u!==Qi);return Qi},charCodeAt(s){return s>=0&&s<t.length?t.charCodeAt(s):0},substring(s,u){return t.substring(s,u)},substrToCursor(s){return this.source.substring(s,this.tokenStart)},cmpChar(s,u){return xe(t,s,u)},cmpStr(s,u,p){return ke(t,s,u,p)},consume(s){let u=this.tokenStart;return this.eat(s),this.substrToCursor(u)},consumeFunctionName(){let s=t.substring(this.tokenStart,this.tokenEnd-1);return this.eat(2),s},consumeNumber(s){let u=t.substring(this.tokenStart,Me(t,this.tokenStart));return this.eat(s),u},eat(s){if(this.tokenType!==s){let u=He[s].slice(0,-6).replace(/-/g," ").replace(/^./,m=>m.toUpperCase()),p=`${/[[\](){}]/.test(u)?`"${u}"`:u} is expected`,h=this.tokenStart;switch(s){case 1:this.tokenType===2||this.tokenType===7?(h=this.tokenEnd-1,p="Identifier is expected but function found"):p="Identifier is expected";break;case 4:this.isDelim(Zl)&&(this.next(),h++,p="Name is expected");break;case 11:this.tokenType===10&&(h=this.tokenEnd,p="Percent sign is expected");break}this.error(p,h)}this.next()},eatIdent(s){(this.tokenType!==1||this.lookupValue(0,s)===!1)&&this.error(`Identifier "${s}" is expected`),this.next()},eatDelim(s){this.isDelim(s)||this.error(`Delim "${String.fromCharCode(s)}" is expected`),this.next()},getLocation(s,u){return n?a.getLocationRange(s,u,r):null},getLocationFromList(s){if(n){let u=this.getFirstListNode(s),p=this.getLastListNode(s);return a.getLocationRange(u!==null?u.loc.start.offset-a.startOffset:this.tokenStart,p!==null?p.loc.end.offset-a.startOffset:this.tokenStart,r)}return null},error(s,u){let p=typeof u<"u"&&u<t.length?a.getLocation(u):this.eof?a.getLocation(_i(t,t.length-1)):a.getLocation(this.tokenStart);throw new Pr(s||"Unexpected input",t,p.offset,p.line,p.column)}});return Object.assign(function(s,u){t=s,u=u||{},l.setSource(t,Le),a.setSource(t,u.offset,u.line,u.column),r=u.filename||"<unknown>",n=Boolean(u.positions),o=typeof u.onParseError=="function"?u.onParseError:Gi,i=!1,l.parseAtrulePrelude="parseAtrulePrelude"in u?Boolean(u.parseAtrulePrelude):!0,l.parseRulePrelude="parseRulePrelude"in u?Boolean(u.parseRulePrelude):!0,l.parseValue="parseValue"in u?Boolean(u.parseValue):!0,l.parseCustomProperty="parseCustomProperty"in u?Boolean(u.parseCustomProperty):!1;let{context:p="default",onComment:h}=u;if(!(p in l.context))throw new Error("Unknown context `"+p+"`");typeof h=="function"&&l.forEachToken((b,T,N)=>{if(b===25){let C=l.getLocation(T,N),D=ke(t,N-2,N,"*/")?t.slice(T+2,N-2):t.slice(T+2,N);h(D,C)}});let m=l.context[p].call(l,u);return l.eof||l.error(),m},{SyntaxError:Pr,config:l.config})}var ba=ql(da(),1),ga=new Set(["Atrule","Selector","Declaration"]);function ya(e){let t=new ba.SourceMapGenerator,r={line:1,column:0},n={line:0,column:0},o={line:1,column:0},i={generated:o},a=1,l=0,c=!1,s=e.node;e.node=function(h){if(h.loc&&h.loc.start&&ga.has(h.type)){let m=h.loc.start.line,b=h.loc.start.column-1;(n.line!==m||n.column!==b)&&(n.line=m,n.column=b,r.line=a,r.column=l,c&&(c=!1,(r.line!==o.line||r.column!==o.column)&&t.addMapping(i)),c=!0,t.addMapping({source:h.loc.source,original:n,generated:r}))}s.call(this,h),c&&ga.has(h.type)&&(o.line=a,o.column=l)};let u=e.emit;e.emit=function(h,m,b){for(let T=0;T<h.length;T++)h.charCodeAt(T)===10?(a++,l=0):l++;u(h,m,b)};let p=e.result;return e.result=function(){return c&&t.addMapping(i),{css:p(),map:t}},e}var $t={};y($t,{safe:()=>_r,spec:()=>vc});var xc=43,kc=45,Br=(e,t)=>{if(e===9&&(e=t),typeof e=="string"){let r=e.charCodeAt(0);return r>127?32768:r<<8}return e},xa=[[1,1],[1,2],[1,7],[1,8],[1,"-"],[1,10],[1,11],[1,12],[1,15],[1,21],[3,1],[3,2],[3,7],[3,8],[3,"-"],[3,10],[3,11],[3,12],[3,15],[4,1],[4,2],[4,7],[4,8],[4,"-"],[4,10],[4,11],[4,12],[4,15],[12,1],[12,2],[12,7],[12,8],[12,"-"],[12,10],[12,11],[12,12],[12,15],["#",1],["#",2],["#",7],["#",8],["#","-"],["#",10],["#",11],["#",12],["#",15],["-",1],["-",2],["-",7],["-",8],["-","-"],["-",10],["-",11],["-",12],["-",15],[10,1],[10,2],[10,7],[10,8],[10,10],[10,11],[10,12],[10,"%"],[10,15],["@",1],["@",2],["@",7],["@",8],["@","-"],["@",15],[".",10],[".",11],[".",12],["+",10],["+",11],["+",12],["/","*"]],wc=xa.concat([[1,4],[12,4],[4,4],[3,21],[3,5],[3,16],[11,11],[11,12],[11,2],[11,"-"],[22,1],[22,2],[22,11],[22,12],[22,4],[22,"-"]]);function ka(e){let t=new Set(e.map(([r,n])=>Br(r)<<16|Br(n)));return function(r,n,o){let i=Br(n,o),a=o.charCodeAt(0);return(a===kc&&n!==1&&n!==2&&n!==15||a===xc?t.has(r<<16|a<<8):t.has(r<<16|i))&&this.emit(" ",13,!0),i}}var vc=ka(xa),_r=ka(wc);var Sc=92;function Cc(e,t){if(typeof t=="function"){let r=null;e.children.forEach(n=>{r!==null&&t.call(this,r),this.node(n),r=n});return}e.children.forEach(this.node,this)}function Ac(e){Le(e,(t,r,n)=>{this.token(t,e.slice(r,n))})}function wa(e){let t=new Map;for(let r in e.node){let n=e.node[r];typeof(n.generate||n)=="function"&&t.set(r,n.generate||n)}return function(r,n){let o="",i=0,a={node(c){if(t.has(c.type))t.get(c.type).call(l,c);else throw new Error("Unknown node type: "+c.type)},tokenBefore:_r,token(c,s){i=this.tokenBefore(i,c,s),this.emit(s,c,!1),c===9&&s.charCodeAt(0)===Sc&&this.emit(`
`,13,!0)},emit(c){o+=c},result(){return o}};n&&(typeof n.decorator=="function"&&(a=n.decorator(a)),n.sourceMap&&(a=ya(a)),n.mode in $t&&(a.tokenBefore=$t[n.mode]));let l={node:c=>a.node(c),children:Cc,token:(c,s)=>a.token(c,s),tokenize:Ac};return a.node(r),a.result()}}function va(e){return{fromPlainObject(t){return e(t,{enter(r){r.children&&!(r.children instanceof w)&&(r.children=new w().fromArray(r.children))}}),t},toPlainObject(t){return e(t,{leave(r){r.children&&r.children instanceof w&&(r.children=r.children.toArray())}}),t}}}var{hasOwnProperty:Ur}=Object.prototype,bt=function(){};function Sa(e){return typeof e=="function"?e:bt}function Ca(e,t){return function(r,n,o){r.type===t&&e.call(this,r,n,o)}}function Ec(e,t){let r=t.structure,n=[];for(let o in r){if(Ur.call(r,o)===!1)continue;let i=r[o],a={name:o,type:!1,nullable:!1};Array.isArray(i)||(i=[i]);for(let l of i)l===null?a.nullable=!0:typeof l=="string"?a.type="node":Array.isArray(l)&&(a.type="list");a.type&&n.push(a)}return n.length?{context:t.walkContext,fields:n}:null}function Tc(e){let t={};for(let r in e.node)if(Ur.call(e.node,r)){let n=e.node[r];if(!n.structure)throw new Error("Missed `structure` field in `"+r+"` node type definition");t[r]=Ec(r,n)}return t}function Aa(e,t){let r=e.fields.slice(),n=e.context,o=typeof n=="string";return t&&r.reverse(),function(i,a,l,c){let s;o&&(s=a[n],a[n]=i);for(let u of r){let p=i[u.name];if(!u.nullable||p){if(u.type==="list"){if(t?p.reduceRight(c,!1):p.reduce(c,!1))return!0}else if(l(p))return!0}}o&&(a[n]=s)}}function Ea({StyleSheet:e,Atrule:t,Rule:r,Block:n,DeclarationList:o}){return{Atrule:{StyleSheet:e,Atrule:t,Rule:r,Block:n},Rule:{StyleSheet:e,Atrule:t,Rule:r,Block:n},Declaration:{StyleSheet:e,Atrule:t,Rule:r,Block:n,DeclarationList:o}}}function Ta(e){let t=Tc(e),r={},n={},o=Symbol("break-walk"),i=Symbol("skip-node");for(let s in t)Ur.call(t,s)&&t[s]!==null&&(r[s]=Aa(t[s],!1),n[s]=Aa(t[s],!0));let a=Ea(r),l=Ea(n),c=function(s,u){function p(C,D,Ae){let B=h.call(N,C,D,Ae);return B===o?!0:B===i?!1:!!(b.hasOwnProperty(C.type)&&b[C.type](C,N,p,T)||m.call(N,C,D,Ae)===o)}let h=bt,m=bt,b=r,T=(C,D,Ae,B)=>C||p(D,Ae,B),N={break:o,skip:i,root:s,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if(typeof u=="function")h=u;else if(u&&(h=Sa(u.enter),m=Sa(u.leave),u.reverse&&(b=n),u.visit)){if(a.hasOwnProperty(u.visit))b=u.reverse?l[u.visit]:a[u.visit];else if(!t.hasOwnProperty(u.visit))throw new Error("Bad value `"+u.visit+"` for `visit` option (should be: "+Object.keys(t).sort().join(", ")+")");h=Ca(h,u.visit),m=Ca(m,u.visit)}if(h===bt&&m===bt)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");p(s)};return c.break=o,c.skip=i,c.find=function(s,u){let p=null;return c(s,function(h,m,b){if(u.call(this,h,m,b))return p=h,o}),p},c.findLast=function(s,u){let p=null;return c(s,{reverse:!0,enter(h,m,b){if(u.call(this,h,m,b))return p=h,o}}),p},c.findAll=function(s,u){let p=[];return c(s,function(h,m,b){u.call(this,h,m,b)&&p.push(h)}),p},c}function Lc(e){return e}function Pc(e){let{min:t,max:r,comma:n}=e;return t===0&&r===0?n?"#?":"*":t===0&&r===1?"?":t===1&&r===0?n?"#":"+":t===1&&r===1?"":(n?"#":"")+(t===r?"{"+t+"}":"{"+t+","+(r!==0?r:"")+"}")}function Ic(e){switch(e.type){case"Range":return" ["+(e.min===null?"-\u221E":e.min)+","+(e.max===null?"\u221E":e.max)+"]";default:throw new Error("Unknown node type `"+e.type+"`")}}function Oc(e,t,r,n){let o=e.combinator===" "||n?e.combinator:" "+e.combinator+" ",i=e.terms.map(a=>jr(a,t,r,n)).join(o);return e.explicit||r?(n||i[0]===","?"[":"[ ")+i+(n?"]":" ]"):i}function jr(e,t,r,n){let o;switch(e.type){case"Group":o=Oc(e,t,r,n)+(e.disallowEmpty?"!":"");break;case"Multiplier":return jr(e.term,t,r,n)+t(Pc(e),e);case"Type":o="<"+e.name+(e.opts?t(Ic(e.opts),e.opts):"")+">";break;case"Property":o="<'"+e.name+"'>";break;case"Keyword":o=e.name;break;case"AtKeyword":o="@"+e.name;break;case"Function":o=e.name+"(";break;case"String":case"Token":o=e.value;break;case"Comma":o=",";break;default:throw new Error("Unknown node type `"+e.type+"`")}return t(o,e)}function Ge(e,t){let r=Lc,n=!1,o=!1;return typeof t=="function"?r=t:t&&(n=Boolean(t.forceBraces),o=Boolean(t.compact),typeof t.decorate=="function"&&(r=t.decorate)),jr(e,r,n,o)}var La={offset:0,line:1,column:1};function Dc(e,t){let r=e.tokens,n=e.longestMatch,o=n<r.length&&r[n].node||null,i=o!==t?o:null,a=0,l=0,c=0,s="",u,p;for(let h=0;h<r.length;h++){let m=r[h].value;h===n&&(l=m.length,a=s.length),i!==null&&r[h].node===i&&(h<=n?c++:c=0),s+=m}return n===r.length||c>1?(u=Xt(i||t,"end")||yt(La,s),p=yt(u)):(u=Xt(i,"start")||yt(Xt(t,"start")||La,s.slice(0,a)),p=Xt(i,"end")||yt(u,s.substr(a,l))),{css:s,mismatchOffset:a,mismatchLength:l,start:u,end:p}}function Xt(e,t){let r=e&&e.loc&&e.loc[t];return r?"line"in r?yt(r):r:null}function yt({offset:e,line:t,column:r},n){let o={offset:e,line:t,column:r};if(n){let i=n.split(/\n|\r\n?|\f/);o.offset+=n.length,o.line+=i.length-1,o.column=i.length===1?o.column+n.length:i.pop().length+1}return o}var Ke=function(e,t){let r=Re("SyntaxReferenceError",e+(t?" `"+t+"`":""));return r.reference=t,r},Pa=function(e,t,r,n){let o=Re("SyntaxMatchError",e),{css:i,mismatchOffset:a,mismatchLength:l,start:c,end:s}=Dc(n,r);return o.rawMessage=e,o.syntax=t?Ge(t):"<generic>",o.css=i,o.mismatchOffset=a,o.mismatchLength=l,o.message=e+`
  syntax: `+o.syntax+`
   value: `+(i||"<empty string>")+`
  --------`+new Array(o.mismatchOffset+1).join("-")+"^",Object.assign(o,c),o.loc={source:r&&r.loc&&r.loc.source||"<unknown>",start:c,end:s},o};var Zt=new Map,Qe=new Map,Jt=45,Z=Nc,me=Mc;function er(e,t){return t=t||0,e.length-t>=2&&e.charCodeAt(t)===Jt&&e.charCodeAt(t+1)===Jt}function Ia(e,t){if(t=t||0,e.length-t>=3&&e.charCodeAt(t)===Jt&&e.charCodeAt(t+1)!==Jt){let r=e.indexOf("-",t+2);if(r!==-1)return e.substring(t,r+1)}return""}function Nc(e){if(Zt.has(e))return Zt.get(e);let t=e.toLowerCase(),r=Zt.get(t);if(r===void 0){let n=er(t,0),o=n?"":Ia(t,0);r=Object.freeze({basename:t.substr(o.length),name:t,prefix:o,vendor:o,custom:n})}return Zt.set(e,r),r}function Mc(e){if(Qe.has(e))return Qe.get(e);let t=e,r=e[0];r==="/"?r=e[1]==="/"?"//":"/":r!=="_"&&r!=="*"&&r!=="$"&&r!=="#"&&r!=="+"&&r!=="&"&&(r="");let n=er(t,r.length);if(!n&&(t=t.toLowerCase(),Qe.has(t))){let l=Qe.get(t);return Qe.set(e,l),l}let o=n?"":Ia(t,r.length),i=t.substr(0,r.length+o.length),a=Object.freeze({basename:t.substr(i.length),name:t.substr(r.length),hack:r,vendor:o,prefix:i,custom:n});return Qe.set(e,a),a}var tr=["initial","inherit","unset","revert","revert-layer"];var kt=43,de=45,qr=110,$e=!0,zc=!1;function Wr(e,t){return e!==null&&e.type===9&&e.value.charCodeAt(0)===t}function xt(e,t,r){for(;e!==null&&(e.type===13||e.type===25);)e=r(++t);return t}function Pe(e,t,r,n){if(!e)return 0;let o=e.value.charCodeAt(t);if(o===kt||o===de){if(r)return 0;t++}for(;t<e.value.length;t++)if(!j(e.value.charCodeAt(t)))return 0;return n+1}function Hr(e,t,r){let n=!1,o=xt(e,t,r);if(e=r(o),e===null)return t;if(e.type!==10)if(Wr(e,kt)||Wr(e,de)){if(n=!0,o=xt(r(++o),o,r),e=r(o),e===null||e.type!==10)return 0}else return t;if(!n){let i=e.value.charCodeAt(0);if(i!==kt&&i!==de)return 0}return Pe(e,n?0:1,n,o)}function Yr(e,t){let r=0;if(!e)return 0;if(e.type===10)return Pe(e,0,zc,r);if(e.type===1&&e.value.charCodeAt(0)===de){if(!xe(e.value,1,qr))return 0;switch(e.value.length){case 2:return Hr(t(++r),r,t);case 3:return e.value.charCodeAt(2)!==de?0:(r=xt(t(++r),r,t),e=t(r),Pe(e,0,$e,r));default:return e.value.charCodeAt(2)!==de?0:Pe(e,3,$e,r)}}else if(e.type===1||Wr(e,kt)&&t(r+1).type===1){if(e.type!==1&&(e=t(++r)),e===null||!xe(e.value,0,qr))return 0;switch(e.value.length){case 1:return Hr(t(++r),r,t);case 2:return e.value.charCodeAt(1)!==de?0:(r=xt(t(++r),r,t),e=t(r),Pe(e,0,$e,r));default:return e.value.charCodeAt(1)!==de?0:Pe(e,2,$e,r)}}else if(e.type===12){let n=e.value.charCodeAt(0),o=n===kt||n===de?1:0,i=o;for(;i<e.value.length&&j(e.value.charCodeAt(i));i++);return i===o||!xe(e.value,i,qr)?0:i+1===e.value.length?Hr(t(++r),r,t):e.value.charCodeAt(i+1)!==de?0:i+2===e.value.length?(r=xt(t(++r),r,t),e=t(r),Pe(e,0,$e,r)):Pe(e,i+2,$e,r)}return 0}var Fc=43,Oa=45,Da=63,Bc=117;function Vr(e,t){return e!==null&&e.type===9&&e.value.charCodeAt(0)===t}function _c(e,t){return e.value.charCodeAt(0)===t}function wt(e,t,r){let n=0;for(let o=t;o<e.value.length;o++){let i=e.value.charCodeAt(o);if(i===Oa&&r&&n!==0)return wt(e,t+n+1,!1),6;if(!oe(i)||++n>6)return 0}return n}function rr(e,t,r){if(!e)return 0;for(;Vr(r(t),Da);){if(++e>6)return 0;t++}return t}function Gr(e,t){let r=0;if(e===null||e.type!==1||!xe(e.value,0,Bc)||(e=t(++r),e===null))return 0;if(Vr(e,Fc))return e=t(++r),e===null?0:e.type===1?rr(wt(e,0,!0),++r,t):Vr(e,Da)?rr(1,++r,t):0;if(e.type===10){let n=wt(e,1,!0);return n===0?0:(e=t(++r),e===null?r:e.type===12||e.type===10?!_c(e,Oa)||!wt(e,1,!1)?0:r+1:rr(n,r,t))}return e.type===12?rr(wt(e,1,!0),++r,t):0}var Uc=["calc(","-moz-calc(","-webkit-calc("],Kr=new Map([[2,22],[21,22],[19,20],[23,24]]),jc=["cm","mm","q","in","pt","pc","px","em","rem","ex","rex","cap","rcap","ch","rch","ic","ric","lh","rlh","vw","svw","lvw","dvw","vh","svh","lvh","dvh","vi","svi","lvi","dvi","vb","svb","lvb","dvb","vmin","svmin","lvmin","dvmin","vmax","svmax","lvmax","dvmax","cqw","cqh","cqi","cqb","cqmin","cqmax"],qc=["deg","grad","rad","turn"],Hc=["s","ms"],Wc=["hz","khz"],Yc=["dpi","dpcm","dppx","x"],Vc=["fr"],Gc=["db"],Kc=["st"];function ue(e,t){return t<e.length?e.charCodeAt(t):0}function Ma(e,t){return ke(e,0,e.length,t)}function Ra(e,t){for(let r=0;r<t.length;r++)if(Ma(e,t[r]))return!0;return!1}function za(e,t){return t!==e.length-2?!1:ue(e,t)===92&&j(ue(e,t+1))}function nr(e,t,r){if(e&&e.type==="Range"){let n=Number(r!==void 0&&r!==t.length?t.substr(0,r):t);if(isNaN(n)||e.min!==null&&n<e.min&&typeof e.min!="string"||e.max!==null&&n>e.max&&typeof e.max!="string")return!0}return!1}function Qc(e,t){let r=0,n=[],o=0;e:do{switch(e.type){case 24:case 22:case 20:if(e.type!==r)break e;if(r=n.pop(),n.length===0){o++;break e}break;case 2:case 21:case 19:case 23:n.push(r),r=Kr.get(e.type);break}o++}while(e=t(o));return o}function le(e){return function(t,r,n){return t===null?0:t.type===2&&Ra(t.value,Uc)?Qc(t,r):e(t,r,n)}}function z(e){return function(t){return t===null||t.type!==e?0:1}}function $c(e){if(e===null||e.type!==1)return 0;let t=e.value.toLowerCase();return Ra(t,tr)||Ma(t,"default")?0:1}function Xc(e){return e===null||e.type!==1||ue(e.value,0)!==45||ue(e.value,1)!==45?0:1}function Zc(e){if(e===null||e.type!==4)return 0;let t=e.value.length;if(t!==4&&t!==5&&t!==7&&t!==9)return 0;for(let r=1;r<t;r++)if(!oe(ue(e.value,r)))return 0;return 1}function Jc(e){return e===null||e.type!==4||!je(ue(e.value,1),ue(e.value,2),ue(e.value,3))?0:1}function eu(e,t){if(!e)return 0;let r=0,n=[],o=0;e:do{switch(e.type){case 6:case 8:break e;case 24:case 22:case 20:if(e.type!==r)break e;r=n.pop();break;case 17:if(r===0)break e;break;case 9:if(r===0&&e.value==="!")break e;break;case 2:case 21:case 19:case 23:n.push(r),r=Kr.get(e.type);break}o++}while(e=t(o));return o}function tu(e,t){if(!e)return 0;let r=0,n=[],o=0;e:do{switch(e.type){case 6:case 8:break e;case 24:case 22:case 20:if(e.type!==r)break e;r=n.pop();break;case 2:case 21:case 19:case 23:n.push(r),r=Kr.get(e.type);break}o++}while(e=t(o));return o}function Se(e){return e&&(e=new Set(e)),function(t,r,n){if(t===null||t.type!==12)return 0;let o=Me(t.value,0);if(e!==null){let i=t.value.indexOf("\\",o),a=i===-1||!za(t.value,i)?t.value.substr(o):t.value.substring(o,i);if(e.has(a.toLowerCase())===!1)return 0}return nr(n,t.value,o)?0:1}}function ru(e,t,r){return e===null||e.type!==11||nr(r,e.value,e.value.length-1)?0:1}function Na(e){return typeof e!="function"&&(e=function(){return 0}),function(t,r,n){return t!==null&&t.type===10&&Number(t.value)===0?1:e(t,r,n)}}function nu(e,t,r){if(e===null)return 0;let n=Me(e.value,0);return!(n===e.value.length)&&!za(e.value,n)||nr(r,e.value,n)?0:1}function ou(e,t,r){if(e===null||e.type!==10)return 0;let n=ue(e.value,0)===43||ue(e.value,0)===45?1:0;for(;n<e.value.length;n++)if(!j(ue(e.value,n)))return 0;return nr(r,e.value,n)?0:1}var Qr={"ident-token":z(1),"function-token":z(2),"at-keyword-token":z(3),"hash-token":z(4),"string-token":z(5),"bad-string-token":z(6),"url-token":z(7),"bad-url-token":z(8),"delim-token":z(9),"number-token":z(10),"percentage-token":z(11),"dimension-token":z(12),"whitespace-token":z(13),"CDO-token":z(14),"CDC-token":z(15),"colon-token":z(16),"semicolon-token":z(17),"comma-token":z(18),"[-token":z(19),"]-token":z(20),"(-token":z(21),")-token":z(22),"{-token":z(23),"}-token":z(24),string:z(5),ident:z(1),"custom-ident":$c,"custom-property-name":Xc,"hex-color":Zc,"id-selector":Jc,"an-plus-b":Yr,urange:Gr,"declaration-value":eu,"any-value":tu,dimension:le(Se(null)),angle:le(Se(qc)),decibel:le(Se(Gc)),frequency:le(Se(Wc)),flex:le(Se(Vc)),length:le(Na(Se(jc))),resolution:le(Se(Yc)),semitones:le(Se(Kc)),time:le(Se(Hc)),percentage:le(ru),zero:Na(),number:le(nu),integer:le(ou)};function $r(e,t,r){return Object.assign(Re("SyntaxError",e),{input:t,offset:r,rawMessage:e,message:e+`
  `+t+`
--`+new Array((r||t.length)+1).join("-")+"^"})}var iu=9,au=10,su=12,lu=13,cu=32,or=class{constructor(t){this.str=t,this.pos=0}charCodeAt(t){return t<this.str.length?this.str.charCodeAt(t):0}charCode(){return this.charCodeAt(this.pos)}nextCharCode(){return this.charCodeAt(this.pos+1)}nextNonWsCode(t){return this.charCodeAt(this.findWsEnd(t))}findWsEnd(t){for(;t<this.str.length;t++){let r=this.str.charCodeAt(t);if(r!==lu&&r!==au&&r!==su&&r!==cu&&r!==iu)break}return t}substringToPos(t){return this.str.substring(this.pos,this.pos=t)}eat(t){this.charCode()!==t&&this.error("Expect `"+String.fromCharCode(t)+"`"),this.pos++}peek(){return this.pos<this.str.length?this.str.charAt(this.pos++):""}error(t){throw new $r(t,this.str,this.pos)}};var uu=9,pu=10,hu=12,fu=13,mu=32,Wa=33,Jr=35,Fa=38,ir=39,Ya=40,du=41,Va=42,en=43,tn=44,Ba=45,rn=60,Ga=62,Zr=63,gu=64,cr=91,nn=93,ar=123,_a=124,Ua=125,ja=8734,vt=new Uint8Array(128).map((e,t)=>/[a-zA-Z0-9\-]/.test(String.fromCharCode(t))?1:0),qa={" ":1,"&&":2,"||":3,"|":4};function sr(e){return e.substringToPos(e.findWsEnd(e.pos))}function Xe(e){let t=e.pos;for(;t<e.str.length;t++){let r=e.str.charCodeAt(t);if(r>=128||vt[r]===0)break}return e.pos===t&&e.error("Expect a keyword"),e.substringToPos(t)}function lr(e){let t=e.pos;for(;t<e.str.length;t++){let r=e.str.charCodeAt(t);if(r<48||r>57)break}return e.pos===t&&e.error("Expect a number"),e.substringToPos(t)}function bu(e){let t=e.str.indexOf("'",e.pos+1);return t===-1&&(e.pos=e.str.length,e.error("Expect an apostrophe")),e.substringToPos(t+1)}function Ha(e){let t=null,r=null;return e.eat(ar),t=lr(e),e.charCode()===tn?(e.pos++,e.charCode()!==Ua&&(r=lr(e))):r=t,e.eat(Ua),{min:Number(t),max:r?Number(r):0}}function yu(e){let t=null,r=!1;switch(e.charCode()){case Va:e.pos++,t={min:0,max:0};break;case en:e.pos++,t={min:1,max:0};break;case Zr:e.pos++,t={min:0,max:1};break;case Jr:e.pos++,r=!0,e.charCode()===ar?t=Ha(e):e.charCode()===Zr?(e.pos++,t={min:0,max:0}):t={min:1,max:0};break;case ar:t=Ha(e);break;default:return null}return{type:"Multiplier",comma:r,min:t.min,max:t.max,term:null}}function Ze(e,t){let r=yu(e);return r!==null?(r.term=t,e.charCode()===Jr&&e.charCodeAt(e.pos-1)===en?Ze(e,r):r):t}function Xr(e){let t=e.peek();return t===""?null:{type:"Token",value:t}}function xu(e){let t;return e.eat(rn),e.eat(ir),t=Xe(e),e.eat(ir),e.eat(Ga),Ze(e,{type:"Property",name:t})}function ku(e){let t=null,r=null,n=1;return e.eat(cr),e.charCode()===Ba&&(e.peek(),n=-1),n==-1&&e.charCode()===ja?e.peek():(t=n*Number(lr(e)),vt[e.charCode()]!==0&&(t+=Xe(e))),sr(e),e.eat(tn),sr(e),e.charCode()===ja?e.peek():(n=1,e.charCode()===Ba&&(e.peek(),n=-1),r=n*Number(lr(e)),vt[e.charCode()]!==0&&(r+=Xe(e))),e.eat(nn),{type:"Range",min:t,max:r}}function wu(e){let t,r=null;return e.eat(rn),t=Xe(e),e.charCode()===Ya&&e.nextCharCode()===du&&(e.pos+=2,t+="()"),e.charCodeAt(e.findWsEnd(e.pos))===cr&&(sr(e),r=ku(e)),e.eat(Ga),Ze(e,{type:"Type",name:t,opts:r})}function vu(e){let t=Xe(e);return e.charCode()===Ya?(e.pos++,{type:"Function",name:t}):Ze(e,{type:"Keyword",name:t})}function Su(e,t){function r(o,i){return{type:"Group",terms:o,combinator:i,disallowEmpty:!1,explicit:!1}}let n;for(t=Object.keys(t).sort((o,i)=>qa[o]-qa[i]);t.length>0;){n=t.shift();let o=0,i=0;for(;o<e.length;o++){let a=e[o];a.type==="Combinator"&&(a.value===n?(i===-1&&(i=o-1),e.splice(o,1),o--):(i!==-1&&o-i>1&&(e.splice(i,o-i,r(e.slice(i,o),n)),o=i+1),i=-1))}i!==-1&&t.length&&e.splice(i,o-i,r(e.slice(i,o),n))}return n}function Ka(e){let t=[],r={},n,o=null,i=e.pos;for(;n=Au(e);)n.type!=="Spaces"&&(n.type==="Combinator"?((o===null||o.type==="Combinator")&&(e.pos=i,e.error("Unexpected combinator")),r[n.value]=!0):o!==null&&o.type!=="Combinator"&&(r[" "]=!0,t.push({type:"Combinator",value:" "})),t.push(n),o=n,i=e.pos);return o!==null&&o.type==="Combinator"&&(e.pos-=i,e.error("Unexpected combinator")),{type:"Group",terms:t,combinator:Su(t,r)||" ",disallowEmpty:!1,explicit:!1}}function Cu(e){let t;return e.eat(cr),t=Ka(e),e.eat(nn),t.explicit=!0,e.charCode()===Wa&&(e.pos++,t.disallowEmpty=!0),t}function Au(e){let t=e.charCode();if(t<128&&vt[t]===1)return vu(e);switch(t){case nn:break;case cr:return Ze(e,Cu(e));case rn:return e.nextCharCode()===ir?xu(e):wu(e);case _a:return{type:"Combinator",value:e.substringToPos(e.pos+(e.nextCharCode()===_a?2:1))};case Fa:return e.pos++,e.eat(Fa),{type:"Combinator",value:"&&"};case tn:return e.pos++,{type:"Comma"};case ir:return Ze(e,{type:"String",value:bu(e)});case mu:case uu:case pu:case fu:case hu:return{type:"Spaces",value:sr(e)};case gu:return t=e.nextCharCode(),t<128&&vt[t]===1?(e.pos++,{type:"AtKeyword",name:Xe(e)}):Xr(e);case Va:case en:case Zr:case Jr:case Wa:break;case ar:if(t=e.nextCharCode(),t<48||t>57)return Xr(e);break;default:return Xr(e)}}function St(e){let t=new or(e),r=Ka(t);return t.pos!==e.length&&t.error("Unexpected input"),r.terms.length===1&&r.terms[0].type==="Group"?r.terms[0]:r}var Ct=function(){};function Qa(e){return typeof e=="function"?e:Ct}function on(e,t,r){function n(a){switch(o.call(r,a),a.type){case"Group":a.terms.forEach(n);break;case"Multiplier":n(a.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+a.type)}i.call(r,a)}let o=Ct,i=Ct;if(typeof t=="function"?o=t:t&&(o=Qa(t.enter),i=Qa(t.leave)),o===Ct&&i===Ct)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");n(e,r)}var Tu={decorator(e){let t=[],r=null;return{...e,node(n){let o=r;r=n,e.node.call(this,n),r=o},emit(n,o,i){t.push({type:o,value:n,node:i?null:r})},result(){return t}}}};function Lu(e){let t=[];return Le(e,(r,n,o)=>t.push({type:r,value:e.slice(n,o),node:null})),t}function $a(e,t){return typeof e=="string"?Lu(e):t.generate(e,Tu)}var E={type:"Match"},O={type:"Mismatch"},ur={type:"DisallowEmpty"},Pu=40,Iu=41;function ee(e,t,r){return t===E&&r===O||e===E&&t===E&&r===E?e:(e.type==="If"&&e.else===O&&t===E&&(t=e.then,e=e.match),{type:"If",match:e,then:t,else:r})}function Za(e){return e.length>2&&e.charCodeAt(e.length-2)===Pu&&e.charCodeAt(e.length-1)===Iu}function Xa(e){return e.type==="Keyword"||e.type==="AtKeyword"||e.type==="Function"||e.type==="Type"&&Za(e.name)}function an(e,t,r){switch(e){case" ":{let n=E;for(let o=t.length-1;o>=0;o--){let i=t[o];n=ee(i,n,O)}return n}case"|":{let n=O,o=null;for(let i=t.length-1;i>=0;i--){let a=t[i];if(Xa(a)&&(o===null&&i>0&&Xa(t[i-1])&&(o=Object.create(null),n=ee({type:"Enum",map:o},E,n)),o!==null)){let l=(Za(a.name)?a.name.slice(0,-1):a.name).toLowerCase();if(!(l in o)){o[l]=a;continue}}o=null,n=ee(a,E,n)}return n}case"&&":{if(t.length>5)return{type:"MatchOnce",terms:t,all:!0};let n=O;for(let o=t.length-1;o>=0;o--){let i=t[o],a;t.length>1?a=an(e,t.filter(function(l){return l!==i}),!1):a=E,n=ee(i,a,n)}return n}case"||":{if(t.length>5)return{type:"MatchOnce",terms:t,all:!1};let n=r?E:O;for(let o=t.length-1;o>=0;o--){let i=t[o],a;t.length>1?a=an(e,t.filter(function(l){return l!==i}),!0):a=E,n=ee(i,a,n)}return n}}}function Ou(e){let t=E,r=sn(e.term);if(e.max===0)r=ee(r,ur,O),t=ee(r,null,O),t.then=ee(E,E,t),e.comma&&(t.then.else=ee({type:"Comma",syntax:e},t,O));else for(let n=e.min||1;n<=e.max;n++)e.comma&&t!==E&&(t=ee({type:"Comma",syntax:e},t,O)),t=ee(r,ee(E,E,t),O);if(e.min===0)t=ee(E,E,t);else for(let n=0;n<e.min-1;n++)e.comma&&t!==E&&(t=ee({type:"Comma",syntax:e},t,O)),t=ee(r,t,O);return t}function sn(e){if(typeof e=="function")return{type:"Generic",fn:e};switch(e.type){case"Group":{let t=an(e.combinator,e.terms.map(sn),!1);return e.disallowEmpty&&(t=ee(t,ur,O)),t}case"Multiplier":return Ou(e);case"Type":case"Property":return{type:e.type,name:e.name,syntax:e};case"Keyword":return{type:e.type,name:e.name.toLowerCase(),syntax:e};case"AtKeyword":return{type:e.type,name:"@"+e.name.toLowerCase(),syntax:e};case"Function":return{type:e.type,name:e.name.toLowerCase()+"(",syntax:e};case"String":return e.value.length===3?{type:"Token",value:e.value.charAt(1),syntax:e}:{type:e.type,value:e.value.substr(1,e.value.length-2).replace(/\\'/g,"'"),syntax:e};case"Token":return{type:e.type,value:e.value,syntax:e};case"Comma":return{type:e.type,syntax:e};default:throw new Error("Unknown node type:",e.type)}}function pr(e,t){return typeof e=="string"&&(e=St(e)),{type:"MatchGraph",match:sn(e),syntax:t||null,source:e}}var{hasOwnProperty:Ja}=Object.prototype,Du=0,Nu=1,cn=2,os=3,es="Match",Mu="Mismatch",Ru="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",ts=15e3,zu=0;function Fu(e){let t=null,r=null,n=e;for(;n!==null;)r=n.prev,n.prev=t,t=n,n=r;return t}function ln(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let n=t.charCodeAt(r),o=e.charCodeAt(r);if(o>=65&&o<=90&&(o=o|32),o!==n)return!1}return!0}function Bu(e){return e.type!==9?!1:e.value!=="?"}function rs(e){return e===null?!0:e.type===18||e.type===2||e.type===21||e.type===19||e.type===23||Bu(e)}function ns(e){return e===null?!0:e.type===22||e.type===20||e.type===24||e.type===9&&e.value==="/"}function _u(e,t,r){function n(){do D++,C=D<e.length?e[D]:null;while(C!==null&&(C.type===13||C.type===25))}function o(ce){let ye=D+ce;return ye<e.length?e[ye]:null}function i(ce,ye){return{nextState:ce,matchStack:B,syntaxStack:p,thenStack:h,tokenIndex:D,prev:ye}}function a(ce){h={nextState:ce,matchStack:B,syntaxStack:p,prev:h}}function l(ce){m=i(ce,m)}function c(){B={type:Nu,syntax:t.syntax,token:C,prev:B},n(),b=null,D>Ae&&(Ae=D)}function s(){p={syntax:t.syntax,opts:t.syntax.opts||p!==null&&p.opts||null,prev:p},B={type:cn,syntax:t.syntax,token:B.token,prev:B}}function u(){B.type===cn?B=B.prev:B={type:os,syntax:p.syntax,token:B.token,prev:B},p=p.prev}let p=null,h=null,m=null,b=null,T=0,N=null,C=null,D=-1,Ae=0,B={type:Du,syntax:null,token:null,prev:null};for(n();N===null&&++T<ts;)switch(t.type){case"Match":if(h===null){if(C!==null&&(D!==e.length-1||C.value!=="\\0"&&C.value!=="\\9")){t=O;break}N=es;break}if(t=h.nextState,t===ur)if(h.matchStack===B){t=O;break}else t=E;for(;h.syntaxStack!==p;)u();h=h.prev;break;case"Mismatch":if(b!==null&&b!==!1)(m===null||D>m.tokenIndex)&&(m=b,b=!1);else if(m===null){N=Mu;break}t=m.nextState,h=m.thenStack,p=m.syntaxStack,B=m.matchStack,D=m.tokenIndex,C=D<e.length?e[D]:null,m=m.prev;break;case"MatchGraph":t=t.match;break;case"If":t.else!==O&&l(t.else),t.then!==E&&a(t.then),t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",syntax:t,index:0,mask:0};break;case"MatchOnceBuffer":{let J=t.syntax.terms;if(t.index===J.length){if(t.mask===0||t.syntax.all){t=O;break}t=E;break}if(t.mask===(1<<J.length)-1){t=E;break}for(;t.index<J.length;t.index++){let te=1<<t.index;if((t.mask&te)===0){l(t),a({type:"AddMatchOnce",syntax:t.syntax,mask:t.mask|te}),t=J[t.index++];break}}break}case"AddMatchOnce":t={type:"MatchOnceBuffer",syntax:t.syntax,index:0,mask:t.mask};break;case"Enum":if(C!==null){let J=C.value.toLowerCase();if(J.indexOf("\\")!==-1&&(J=J.replace(/\\[09].*$/,"")),Ja.call(t.map,J)){t=t.map[J];break}}t=O;break;case"Generic":{let J=p!==null?p.opts:null,te=D+Math.floor(t.fn(C,o,J));if(!isNaN(te)&&te>D){for(;D<te;)c();t=E}else t=O;break}case"Type":case"Property":{let J=t.type==="Type"?"types":"properties",te=Ja.call(r,J)?r[J][t.name]:null;if(!te||!te.match)throw new Error("Bad syntax reference: "+(t.type==="Type"?"<"+t.name+">":"<'"+t.name+"'>"));if(b!==!1&&C!==null&&t.type==="Type"&&(t.name==="custom-ident"&&C.type===1||t.name==="length"&&C.value==="0")){b===null&&(b=i(t,m)),t=O;break}s(),t=te.match;break}case"Keyword":{let J=t.name;if(C!==null){let te=C.value;if(te.indexOf("\\")!==-1&&(te=te.replace(/\\[09].*$/,"")),ln(te,J)){c(),t=E;break}}t=O;break}case"AtKeyword":case"Function":if(C!==null&&ln(C.value,t.name)){c(),t=E;break}t=O;break;case"Token":if(C!==null&&C.value===t.value){c(),t=E;break}t=O;break;case"Comma":C!==null&&C.type===18?rs(B.token)?t=O:(c(),t=ns(C)?O:E):t=rs(B.token)||ns(C)?E:O;break;case"String":let ce="",ye=D;for(;ye<e.length&&ce.length<t.value.length;ye++)ce+=e[ye].value;if(ln(ce,t.value)){for(;D<ye;)c();t=E}else t=O;break;default:throw new Error("Unknown node type: "+t.type)}switch(zu+=T,N){case null:console.warn("[csstree-match] BREAK after "+ts+" iterations"),N=Ru,B=null;break;case es:for(;p!==null;)u();break;default:B=null}return{tokens:e,reason:N,iterations:T,match:B,longestMatch:Ae}}function un(e,t,r){let n=_u(e,t,r||{});if(n.match===null)return n;let o=n.match,i=n.match={syntax:t.syntax||null,match:[]},a=[i];for(o=Fu(o).prev;o!==null;){switch(o.type){case cn:i.match.push(i={syntax:o.syntax,match:[]}),a.push(i);break;case os:a.pop(),i=a[a.length-1];break;default:i.match.push({syntax:o.syntax||null,token:o.token.value,node:o.token.node})}o=o.prev}return n}var hn={};y(hn,{getTrace:()=>is,isKeyword:()=>qu,isProperty:()=>ju,isType:()=>Uu});function is(e){function t(o){return o===null?!1:o.type==="Type"||o.type==="Property"||o.type==="Keyword"}function r(o){if(Array.isArray(o.match)){for(let i=0;i<o.match.length;i++)if(r(o.match[i]))return t(o.syntax)&&n.unshift(o.syntax),!0}else if(o.node===e)return n=t(o.syntax)?[o.syntax]:[],!0;return!1}let n=null;return this.matched!==null&&r(this.matched),n}function Uu(e,t){return pn(this,e,r=>r.type==="Type"&&r.name===t)}function ju(e,t){return pn(this,e,r=>r.type==="Property"&&r.name===t)}function qu(e){return pn(this,e,t=>t.type==="Keyword")}function pn(e,t,r){let n=is.call(e,t);return n===null?!1:n.some(r)}function as(e){return"node"in e?e.node:as(e.match[0])}function ss(e){return"node"in e?e.node:ss(e.match[e.match.length-1])}function fn(e,t,r,n,o){function i(l){if(l.syntax!==null&&l.syntax.type===n&&l.syntax.name===o){let c=as(l),s=ss(l);e.syntax.walk(t,function(u,p,h){if(u===c){let m=new w;do{if(m.appendData(p.data),p.data===s)break;p=p.next}while(p!==null);a.push({parent:h,nodes:m})}})}Array.isArray(l.match)&&l.match.forEach(i)}let a=[];return r.matched!==null&&i(r.matched),a}var{hasOwnProperty:At}=Object.prototype;function mn(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&e>=0}function ls(e){return Boolean(e)&&mn(e.offset)&&mn(e.line)&&mn(e.column)}function Hu(e,t){return function(n,o){if(!n||n.constructor!==Object)return o(n,"Type of node should be an Object");for(let i in n){let a=!0;if(At.call(n,i)!==!1){if(i==="type")n.type!==e&&o(n,"Wrong node type `"+n.type+"`, expected `"+e+"`");else if(i==="loc"){if(n.loc===null)continue;if(n.loc&&n.loc.constructor===Object)if(typeof n.loc.source!="string")i+=".source";else if(!ls(n.loc.start))i+=".start";else if(!ls(n.loc.end))i+=".end";else continue;a=!1}else if(t.hasOwnProperty(i)){a=!1;for(let l=0;!a&&l<t[i].length;l++){let c=t[i][l];switch(c){case String:a=typeof n[i]=="string";break;case Boolean:a=typeof n[i]=="boolean";break;case null:a=n[i]===null;break;default:typeof c=="string"?a=n[i]&&n[i].type===c:Array.isArray(c)&&(a=n[i]instanceof w)}}}else o(n,"Unknown field `"+i+"` for "+e+" node type");a||o(n,"Bad value for `"+e+"."+i+"`")}}for(let i in t)At.call(t,i)&&At.call(n,i)===!1&&o(n,"Field `"+e+"."+i+"` is missed")}}function Wu(e,t){let r=t.structure,n={type:String,loc:!0},o={type:'"'+e+'"'};for(let i in r){if(At.call(r,i)===!1)continue;let a=[],l=n[i]=Array.isArray(r[i])?r[i].slice():[r[i]];for(let c=0;c<l.length;c++){let s=l[c];if(s===String||s===Boolean)a.push(s.name);else if(s===null)a.push("null");else if(typeof s=="string")a.push("<"+s+">");else if(Array.isArray(s))a.push("List");else throw new Error("Wrong value `"+s+"` in `"+e+"."+i+"` structure definition")}o[i]=a.join(" | ")}return{docs:o,check:Hu(e,n)}}function cs(e){let t={};if(e.node){for(let r in e.node)if(At.call(e.node,r)){let n=e.node[r];if(n.structure)t[r]=Wu(r,n);else throw new Error("Missed `structure` field in `"+r+"` node type definition")}}return t}var Yu=pr(tr.join(" | "));function dn(e,t,r){let n={};for(let o in e)e[o].syntax&&(n[o]=r?e[o].syntax:Ge(e[o].syntax,{compact:t}));return n}function Vu(e,t,r){let n={};for(let[o,i]of Object.entries(e))n[o]={prelude:i.prelude&&(r?i.prelude.syntax:Ge(i.prelude.syntax,{compact:t})),descriptors:i.descriptors&&dn(i.descriptors,t,r)};return n}function Gu(e){for(let t=0;t<e.length;t++)if(e[t].value.toLowerCase()==="var(")return!0;return!1}function ge(e,t,r){return{matched:e,iterations:r,error:t,...hn}}function Je(e,t,r,n){let o=$a(r,e.syntax),i;return Gu(o)?ge(null,new Error("Matching for a tree with var() is not supported")):(n&&(i=un(o,e.cssWideKeywordsSyntax,e)),(!n||!i.match)&&(i=un(o,t.match,e),!i.match)?ge(null,new Pa(i.reason,t.syntax,r,i),i.iterations):ge(i.match,null,i.iterations))}var et=class{constructor(t,r,n){if(this.cssWideKeywordsSyntax=Yu,this.syntax=r,this.generic=!1,this.atrules=Object.create(null),this.properties=Object.create(null),this.types=Object.create(null),this.structure=n||cs(t),t){if(t.types)for(let o in t.types)this.addType_(o,t.types[o]);if(t.generic){this.generic=!0;for(let o in Qr)this.addType_(o,Qr[o])}if(t.atrules)for(let o in t.atrules)this.addAtrule_(o,t.atrules[o]);if(t.properties)for(let o in t.properties)this.addProperty_(o,t.properties[o])}}checkStructure(t){function r(i,a){o.push({node:i,message:a})}let n=this.structure,o=[];return this.syntax.walk(t,function(i){n.hasOwnProperty(i.type)?n[i.type].check(i,r):r(i,"Unknown node type `"+i.type+"`")}),o.length?o:!1}createDescriptor(t,r,n,o=null){let i={type:r,name:n},a={type:r,name:n,parent:o,serializable:typeof t=="string"||t&&typeof t.type=="string",syntax:null,match:null};return typeof t=="function"?a.match=pr(t,i):(typeof t=="string"?Object.defineProperty(a,"syntax",{get(){return Object.defineProperty(a,"syntax",{value:St(t)}),a.syntax}}):a.syntax=t,Object.defineProperty(a,"match",{get(){return Object.defineProperty(a,"match",{value:pr(a.syntax,i)}),a.match}})),a}addAtrule_(t,r){!r||(this.atrules[t]={type:"Atrule",name:t,prelude:r.prelude?this.createDescriptor(r.prelude,"AtrulePrelude",t):null,descriptors:r.descriptors?Object.keys(r.descriptors).reduce((n,o)=>(n[o]=this.createDescriptor(r.descriptors[o],"AtruleDescriptor",o,t),n),Object.create(null)):null})}addProperty_(t,r){!r||(this.properties[t]=this.createDescriptor(r,"Property",t))}addType_(t,r){!r||(this.types[t]=this.createDescriptor(r,"Type",t))}checkAtruleName(t){if(!this.getAtrule(t))return new Ke("Unknown at-rule","@"+t)}checkAtrulePrelude(t,r){let n=this.checkAtruleName(t);if(n)return n;let o=this.getAtrule(t);if(!o.prelude&&r)return new SyntaxError("At-rule `@"+t+"` should not contain a prelude");if(o.prelude&&!r&&!Je(this,o.prelude,"",!1).matched)return new SyntaxError("At-rule `@"+t+"` should contain a prelude")}checkAtruleDescriptorName(t,r){let n=this.checkAtruleName(t);if(n)return n;let o=this.getAtrule(t),i=Z(r);if(!o.descriptors)return new SyntaxError("At-rule `@"+t+"` has no known descriptors");if(!o.descriptors[i.name]&&!o.descriptors[i.basename])return new Ke("Unknown at-rule descriptor",r)}checkPropertyName(t){if(!this.getProperty(t))return new Ke("Unknown property",t)}matchAtrulePrelude(t,r){let n=this.checkAtrulePrelude(t,r);return n?ge(null,n):Je(this,this.getAtrule(t).prelude,r||"",!1)}matchAtruleDescriptor(t,r,n){let o=this.checkAtruleDescriptorName(t,r);if(o)return ge(null,o);let i=this.getAtrule(t),a=Z(r);return Je(this,i.descriptors[a.name]||i.descriptors[a.basename],n,!1)}matchDeclaration(t){return t.type!=="Declaration"?ge(null,new Error("Not a Declaration node")):this.matchProperty(t.property,t.value)}matchProperty(t,r){if(me(t).custom)return ge(null,new Error("Lexer matching doesn't applicable for custom properties"));let n=this.checkPropertyName(t);return n?ge(null,n):Je(this,this.getProperty(t),r,!0)}matchType(t,r){let n=this.getType(t);return n?Je(this,n,r,!1):ge(null,new Ke("Unknown type",t))}match(t,r){return typeof t!="string"&&(!t||!t.type)?ge(null,new Ke("Bad syntax")):((typeof t=="string"||!t.match)&&(t=this.createDescriptor(t,"Type","anonymous")),Je(this,t,r,!1))}findValueFragments(t,r,n,o){return fn(this,r,this.matchProperty(t,r),n,o)}findDeclarationValueFragments(t,r,n){return fn(this,t.value,this.matchDeclaration(t),r,n)}findAllFragments(t,r,n){let o=[];return this.syntax.walk(t,{visit:"Declaration",enter:i=>{o.push.apply(o,this.findDeclarationValueFragments(i,r,n))}}),o}getAtrule(t,r=!0){let n=Z(t);return(n.vendor&&r?this.atrules[n.name]||this.atrules[n.basename]:this.atrules[n.name])||null}getAtrulePrelude(t,r=!0){let n=this.getAtrule(t,r);return n&&n.prelude||null}getAtruleDescriptor(t,r){return this.atrules.hasOwnProperty(t)&&this.atrules.declarators&&this.atrules[t].declarators[r]||null}getProperty(t,r=!0){let n=me(t);return(n.vendor&&r?this.properties[n.name]||this.properties[n.basename]:this.properties[n.name])||null}getType(t){return hasOwnProperty.call(this.types,t)?this.types[t]:null}validate(){function t(o,i,a,l){if(a.has(i))return a.get(i);a.set(i,!1),l.syntax!==null&&on(l.syntax,function(c){if(c.type!=="Type"&&c.type!=="Property")return;let s=c.type==="Type"?o.types:o.properties,u=c.type==="Type"?r:n;(!hasOwnProperty.call(s,c.name)||t(o,c.name,u,s[c.name]))&&a.set(i,!0)},this)}let r=new Map,n=new Map;for(let o in this.types)t(this,o,r,this.types[o]);for(let o in this.properties)t(this,o,n,this.properties[o]);return r=[...r.keys()].filter(o=>r.get(o)),n=[...n.keys()].filter(o=>n.get(o)),r.length||n.length?{types:r,properties:n}:null}dump(t,r){return{generic:this.generic,types:dn(this.types,!r,t),properties:dn(this.properties,!r,t),atrules:Vu(this.atrules,!r,t)}}toString(){return JSON.stringify(this.dump())}};var{hasOwnProperty:tt}=Object.prototype,Ku={generic:!0,types:gn,atrules:{prelude:ps,descriptors:ps},properties:gn,parseContext:Qu,scope:fs,atrule:["parse"],pseudo:["parse"],node:["name","structure","parse","generate","walkContext"]};function hr(e){return e&&e.constructor===Object}function hs(e){return hr(e)?{...e}:e}function Qu(e,t){return Object.assign(e,t)}function fs(e,t){for(let r in t)tt.call(t,r)&&(hr(e[r])?fs(e[r],t[r]):e[r]=hs(t[r]));return e}function us(e,t){return typeof t=="string"&&/^\s*\|/.test(t)?typeof e=="string"?e+t:t.replace(/^\s*\|\s*/,""):t||null}function gn(e,t){if(typeof t=="string")return us(e,t);let r={...e};for(let n in t)tt.call(t,n)&&(r[n]=us(tt.call(e,n)?e[n]:void 0,t[n]));return r}function ps(e,t){let r=gn(e,t);return!hr(r)||Object.keys(r).length?r:null}function Et(e,t,r){for(let n in r)if(tt.call(r,n)!==!1){if(r[n]===!0)tt.call(t,n)&&(e[n]=hs(t[n]));else if(r[n]){if(typeof r[n]=="function"){let o=r[n];e[n]=o({},e[n]),e[n]=o(e[n]||{},t[n])}else if(hr(r[n])){let o={};for(let i in e[n])o[i]=Et({},e[n][i],r[n]);for(let i in t[n])o[i]=Et(o[i]||{},t[n][i],r[n]);e[n]=o}else if(Array.isArray(r[n])){let o={},i=r[n].reduce(function(a,l){return a[l]=!0,a},{});for(let[a,l]of Object.entries(e[n]||{}))o[a]={},l&&Et(o[a],l,i);for(let a in t[n])tt.call(t[n],a)&&(o[a]||(o[a]={}),t[n]&&t[n][a]&&Et(o[a],t[n][a],i));e[n]=o}}}return e}var fr=(e,t)=>Et(e,t,Ku);function ms(e){let t=$i(e),r=Ta(e),n=wa(e),{fromPlainObject:o,toPlainObject:i}=va(r),a={lexer:null,createLexer:l=>new et(l,a,a.lexer.structure),tokenize:Le,parse:t,generate:n,walk:r,find:r.find,findLast:r.findLast,findAll:r.findAll,fromPlainObject:o,toPlainObject:i,fork(l){let c=fr({},e);return ms(typeof l=="function"?l(c,Object.assign):fr(c,l))}};return a.lexer=new et({generic:!0,types:e.types,atrules:e.atrules,properties:e.properties,node:e.node},a),a}var bn=e=>ms(fr({},e));var ds={generic:!0,types:{"absolute-size":"xx-small|x-small|small|medium|large|x-large|xx-large|xxx-large","alpha-value":"<number>|<percentage>","angle-percentage":"<angle>|<percentage>","angular-color-hint":"<angle-percentage>","angular-color-stop":"<color>&&<color-stop-angle>?","angular-color-stop-list":"[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>","animateable-feature":"scroll-position|contents|<custom-ident>",attachment:"scroll|fixed|local","attr()":"attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )","attr-matcher":"['~'|'|'|'^'|'$'|'*']? '='","attr-modifier":"i|s","attribute-selector":"'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'","auto-repeat":"repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )","auto-track-list":"[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?","baseline-position":"[first|last]? baseline","basic-shape":"<inset()>|<circle()>|<ellipse()>|<polygon()>|<path()>","bg-image":"none|<image>","bg-layer":"<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","bg-position":"[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]","bg-size":"[<length-percentage>|auto]{1,2}|cover|contain","blur()":"blur( <length> )","blend-mode":"normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity",box:"border-box|padding-box|content-box","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [['+'|'-'] <calc-product>]*","calc-product":"<calc-value> ['*' <calc-value>|'/' <number>]*","calc-value":"<number>|<dimension>|<percentage>|( <calc-sum> )","cf-final-image":"<image>|<color>","cf-mixing-image":"<percentage>?&&<image>","circle()":"circle( [<shape-radius>]? [at <position>]? )","clamp()":"clamp( <calc-sum>#{3} )","class-selector":"'.' <ident-token>","clip-source":"<url>",color:"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<hex-color>|<named-color>|currentcolor|<deprecated-system-color>","color-stop":"<color-stop-length>|<color-stop-angle>","color-stop-angle":"<angle-percentage>{1,2}","color-stop-length":"<length-percentage>{1,2}","color-stop-list":"[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>",combinator:"'>'|'+'|'~'|['||']","common-lig-values":"[common-ligatures|no-common-ligatures]","compat-auto":"searchfield|textarea|push-button|slider-horizontal|checkbox|radio|square-button|menulist|listbox|meter|progress-bar|button","composite-style":"clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor","compositing-operator":"add|subtract|intersect|exclude","compound-selector":"[<type-selector>? <subclass-selector>* [<pseudo-element-selector> <pseudo-class-selector>*]*]!","compound-selector-list":"<compound-selector>#","complex-selector":"<compound-selector> [<combinator>? <compound-selector>]*","complex-selector-list":"<complex-selector>#","conic-gradient()":"conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","contextual-alt-values":"[contextual|no-contextual]","content-distribution":"space-between|space-around|space-evenly|stretch","content-list":"[<string>|contents|<image>|<counter>|<quote>|<target>|<leader()>|<attr()>]+","content-position":"center|start|end|flex-start|flex-end","content-replacement":"<image>","contrast()":"contrast( [<number-percentage>] )",counter:"<counter()>|<counters()>","counter()":"counter( <counter-name> , <counter-style>? )","counter-name":"<custom-ident>","counter-style":"<counter-style-name>|symbols( )","counter-style-name":"<custom-ident>","counters()":"counters( <counter-name> , <string> , <counter-style>? )","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number [0,1]> , <number> , <number [0,1]> , <number> )","deprecated-system-color":"ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText","discretionary-lig-values":"[discretionary-ligatures|no-discretionary-ligatures]","display-box":"contents|none","display-inside":"flow|flow-root|table|flex|grid|ruby","display-internal":"table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container","display-legacy":"inline-block|inline-list-item|inline-table|inline-flex|inline-grid","display-listitem":"<display-outside>?&&[flow|flow-root]?&&list-item","display-outside":"block|inline|run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[jis78|jis83|jis90|jis04|simplified|traditional]","east-asian-width-values":"[full-width|proportional-width]","element()":"element( <custom-ident> , [first|start|last|first-except]? )|element( <id-selector> )","ellipse()":"ellipse( [<shape-radius>{2}]? [at <position>]? )","ending-shape":"circle|ellipse","env()":"env( <custom-ident> , <declaration-value>? )","explicit-track-list":"[<line-names>? <track-size>]+ <line-names>?","family-name":"<string>|<custom-ident>+","feature-tag-value":"<string> [<integer>|on|off]?","feature-type":"@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation","feature-value-block":"<feature-type> '{' <feature-value-declaration-list> '}'","feature-value-block-list":"<feature-value-block>+","feature-value-declaration":"<custom-ident> : <integer>+ ;","feature-value-declaration-list":"<feature-value-declaration>","feature-value-name":"<custom-ident>","fill-rule":"nonzero|evenodd","filter-function":"<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>","filter-function-list":"[<filter-function>|<url>]+","final-bg-layer":"<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","fit-content()":"fit-content( [<length>|<percentage>] )","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [<integer [1,\u221E]>] , [<line-names>? <fixed-size>]+ <line-names>? )","fixed-size":"<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )","font-stretch-absolute":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>","font-variant-css21":"[normal|small-caps]","font-weight-absolute":"normal|bold|<number [1,1000]>","frequency-percentage":"<frequency>|<percentage>","general-enclosed":"[<function-token> <any-value> )]|( <ident> <any-value> )","generic-family":"serif|sans-serif|cursive|fantasy|monospace|-apple-system","generic-name":"serif|sans-serif|cursive|fantasy|monospace","geometry-box":"<shape-box>|fill-box|stroke-box|view-box",gradient:"<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<repeating-conic-gradient()>|<-legacy-gradient>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]","historical-lig-values":"[historical-ligatures|no-historical-ligatures]","hsl()":"hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number>|<angle>","hue-rotate()":"hue-rotate( <angle> )","hwb()":"hwb( [<hue>|none] [<percentage>|none] [<percentage>|none] [/ [<alpha-value>|none]]? )",image:"<url>|<image()>|<image-set()>|<element()>|<paint()>|<cross-fade()>|<gradient>","image()":"image( <image-tags>? [<image-src>? , <color>?]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[<image>|<string>] [<resolution>||type( <string> )]","image-src":"<url>|<string>","image-tags":"ltr|rtl","inflexible-breadth":"<length>|<percentage>|min-content|max-content|auto","inset()":"inset( <length-percentage>{1,4} [round <'border-radius'>]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident>|<string>","keyframe-block":"<keyframe-selector># { <declaration-list> }","keyframe-block-list":"<keyframe-block>+","keyframe-selector":"from|to|<percentage>","layer()":"layer( <layer-name> )","layer-name":"<ident> ['.' <ident>]*","leader()":"leader( <leader-type> )","leader-type":"dotted|solid|space|<string>","length-percentage":"<length>|<percentage>","line-names":"'[' <custom-ident>* ']'","line-name-list":"[<line-names>|<name-repeat>]+","line-style":"none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset","line-width":"<length>|thin|medium|thick","linear-color-hint":"<length-percentage>","linear-color-stop":"<color> <color-stop-length>?","linear-gradient()":"linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","mask-layer":"<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>","mask-position":"[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?","mask-reference":"none|<image>|<mask-source>","mask-source":"<url>","masking-mode":"alpha|luminance|match-source","matrix()":"matrix( <number>#{6} )","matrix3d()":"matrix3d( <number>#{16} )","max()":"max( <calc-sum># )","media-and":"<media-in-parens> [and <media-in-parens>]+","media-condition":"<media-not>|<media-and>|<media-or>|<media-in-parens>","media-condition-without-or":"<media-not>|<media-and>|<media-in-parens>","media-feature":"( [<mf-plain>|<mf-boolean>|<mf-range>] )","media-in-parens":"( <media-condition> )|<media-feature>|<general-enclosed>","media-not":"not <media-in-parens>","media-or":"<media-in-parens> [or <media-in-parens>]+","media-query":"<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?","media-query-list":"<media-query>#","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","mf-plain":"<mf-name> : <mf-value>","mf-range":"<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>","mf-value":"<number>|<dimension>|<ident>|<ratio>","min()":"min( <calc-sum># )","minmax()":"minmax( [<length>|<percentage>|min-content|max-content|auto] , [<length>|<percentage>|<flex>|min-content|max-content|auto] )","name-repeat":"repeat( [<integer [1,\u221E]>|auto-fill] , <line-names>+ )","named-color":"transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen|<-non-standard-color>","namespace-prefix":"<ident>","ns-prefix":"[<ident-token>|'*']? '|'","number-percentage":"<number>|<percentage>","numeric-figure-values":"[lining-nums|oldstyle-nums]","numeric-fraction-values":"[diagonal-fractions|stacked-fractions]","numeric-spacing-values":"[proportional-nums|tabular-nums]",nth:"<an-plus-b>|even|odd","opacity()":"opacity( [<number-percentage>] )","overflow-position":"unsafe|safe","outline-radius":"<length>|<percentage>","page-body":"<declaration>? [; <page-body>]?|<page-margin-box> <page-body>","page-margin-box":"<page-margin-box-type> '{' <declaration-list> '}'","page-margin-box-type":"@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom","page-selector-list":"[<page-selector>#]?","page-selector":"<pseudo-page>+|<ident> <pseudo-page>*","page-size":"A5|A4|A3|B5|B4|JIS-B5|JIS-B4|letter|legal|ledger","path()":"path( [<fill-rule> ,]? <string> )","paint()":"paint( <ident> , <declaration-value>? )","perspective()":"perspective( <length> )","polygon()":"polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )",position:"[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]","pseudo-class-selector":"':' <ident-token>|':' <function-token> <any-value> ')'","pseudo-element-selector":"':' <pseudo-class-selector>","pseudo-page":": [left|right|first|blank]",quote:"open-quote|close-quote|no-open-quote|no-close-quote","radial-gradient()":"radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","relative-selector":"<combinator>? <complex-selector>","relative-selector-list":"<relative-selector>#","relative-size":"larger|smaller","repeat-style":"repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}","repeating-conic-gradient()":"repeating-conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","repeating-linear-gradient()":"repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","rgb()":"rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( [<angle>|<zero>] )","rotate3d()":"rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )","rotateX()":"rotateX( [<angle>|<zero>] )","rotateY()":"rotateY( [<angle>|<zero>] )","rotateZ()":"rotateZ( [<angle>|<zero>] )","saturate()":"saturate( <number-percentage> )","scale()":"scale( <number> , <number>? )","scale3d()":"scale3d( <number> , <number> , <number> )","scaleX()":"scaleX( <number> )","scaleY()":"scaleY( <number> )","scaleZ()":"scaleZ( <number> )","self-position":"center|start|end|self-start|self-end|flex-start|flex-end","shape-radius":"<length-percentage>|closest-side|farthest-side","skew()":"skew( [<angle>|<zero>] , [<angle>|<zero>]? )","skewX()":"skewX( [<angle>|<zero>] )","skewY()":"skewY( [<angle>|<zero>] )","sepia()":"sepia( <number-percentage> )",shadow:"inset?&&<length>{2,4}&&<color>?","shadow-t":"[<length>{2,3}&&<color>?]",shape:"rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )","shape-box":"<box>|margin-box","side-or-corner":"[left|right]||[top|bottom]","single-animation":"<time>||<easing-function>||<time>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]","single-animation-direction":"normal|reverse|alternate|alternate-reverse","single-animation-fill-mode":"none|forwards|backwards|both","single-animation-iteration-count":"infinite|<number>","single-animation-play-state":"running|paused","single-animation-timeline":"auto|none|<timeline-name>","single-transition":"[none|<single-transition-property>]||<time>||<easing-function>||<time>","single-transition-property":"all|<custom-ident>",size:"closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}","step-position":"jump-start|jump-end|jump-none|jump-both|start|end","step-timing-function":"step-start|step-end|steps( <integer> [, <step-position>]? )","subclass-selector":"<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>","supports-condition":"not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*","supports-in-parens":"( <supports-condition> )|<supports-feature>|<general-enclosed>","supports-feature":"<supports-decl>|<supports-selector-fn>","supports-decl":"( <declaration> )","supports-selector-fn":"selector( <complex-selector> )",symbol:"<string>|<image>|<custom-ident>",target:"<target-counter()>|<target-counters()>|<target-text()>","target-counter()":"target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [<string>|<url>] , [content|before|after|first-letter]? )","time-percentage":"<time>|<percentage>","timeline-name":"<custom-ident>|<string>","easing-function":"linear|<cubic-bezier-timing-function>|<step-timing-function>","track-breadth":"<length-percentage>|<flex>|min-content|max-content|auto","track-list":"[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?","track-repeat":"repeat( [<integer [1,\u221E]>] , [<line-names>? <track-size>]+ <line-names>? )","track-size":"<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( [<length>|<percentage>] )","transform-function":"<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>","transform-list":"<transform-function>+","translate()":"translate( <length-percentage> , <length-percentage>? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%","type-selector":"<wq-name>|<ns-prefix>? '*'","var()":"var( <custom-property-name> , <declaration-value>? )","viewport-length":"auto|<length-percentage>","visual-box":"content-box|padding-box|border-box","wq-name":"<ns-prefix>? <ident-token>","-legacy-gradient":"<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[<angle>|<side-or-corner>]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side|closest-corner|farthest-side|farthest-corner|contain|cover","-legacy-radial-gradient-shape":"circle|ellipse","-non-standard-font":"-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text","-non-standard-image-rendering":"optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast","-non-standard-overflow":"-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable","-non-standard-width":"fill-available|min-intrinsic|intrinsic|-moz-available|-moz-fit-content|-moz-min-content|-moz-max-content|-webkit-min-content|-webkit-max-content","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )","-webkit-gradient-color-stop":"from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )","-webkit-gradient-point":"[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]","-webkit-gradient-radius":"<length>|<percentage>","-webkit-gradient-type":"linear|radial","-webkit-mask-box-repeat":"repeat|stretch|round","-webkit-mask-clip-style":"border|border-box|padding|padding-box|content|content-box|text","-ms-filter-function-list":"<-ms-filter-function>+","-ms-filter-function":"<-ms-filter-function-progid>|<-ms-filter-function-legacy>","-ms-filter-function-progid":"'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]","-ms-filter-function-legacy":"<ident-token>|<function-token> <any-value>? )","-ms-filter":"<string>",age:"child|young|old","attr-name":"<wq-name>","attr-fallback":"<any-value>","bg-clip":"<box>|border|text","border-radius":"<length-percentage>{1,2}",bottom:"<length>|auto","generic-voice":"[<age>? <gender> <integer>?]",gender:"male|female|neutral","lab()":"lab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )","lch()":"lch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )",left:"<length>|auto","mask-image":"<mask-reference>#",paint:"none|<color>|<url> [none|<color>]?|context-fill|context-stroke",ratio:"<number [0,\u221E]> [/ <number [0,\u221E]>]?","reversed-counter-name":"reversed( <counter-name> )",right:"<length>|auto","svg-length":"<percentage>|<length>|<number>","svg-writing-mode":"lr-tb|rl-tb|tb-rl|lr|rl|tb",top:"<length>|auto","track-group":"'(' [<string>* <track-minmax> <string>*]+ ')' ['[' <positive-integer> ']']?|<track-minmax>","track-list-v0":"[<string>* <track-group> <string>*]+|none","track-minmax":"minmax( <track-breadth> , <track-breadth> )|auto|<track-breadth>|fit-content",x:"<number>",y:"<number>",declaration:"<ident-token> : <declaration-value>? ['!' important]?","declaration-list":"[<declaration>? ';']* <declaration>?",url:"url( <string> <url-modifier>* )|<url-token>","url-modifier":"<ident>|<function-token> <any-value> )","number-zero-one":"<number [0,1]>","number-one-or-greater":"<number [1,\u221E]>","positive-integer":"<integer [0,\u221E]>","-non-standard-display":"-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box"},properties:{"--*":"<declaration-value>","-ms-accelerator":"false|true","-ms-block-progression":"tb|rl|bt|lr","-ms-content-zoom-chaining":"none|chained","-ms-content-zooming":"none|zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> )|snapList( <percentage># )","-ms-content-zoom-snap-type":"none|proximity|mandatory","-ms-filter":"<string>","-ms-flow-from":"[none|<custom-ident>]#","-ms-flow-into":"[none|<custom-ident>]#","-ms-grid-columns":"none|<track-list>|<auto-track-list>","-ms-grid-rows":"none|<track-list>|<auto-track-list>","-ms-high-contrast-adjust":"auto|none","-ms-hyphenate-limit-chars":"auto|<integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit|<integer>","-ms-hyphenate-limit-zone":"<percentage>|<length>","-ms-ime-align":"auto|after","-ms-overflow-style":"auto|none|scrollbar|-ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained|none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto|<length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto|<length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none|railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-type":"none|proximity|mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none|vertical-to-horizontal","-ms-text-autospace":"none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space","-ms-touch-select":"grippers|none","-ms-user-select":"none|element|text","-ms-wrap-flow":"auto|both|start|end|maximum|clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap|none","-moz-appearance":"none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized","-moz-binding":"<url>|none","-moz-border-bottom-colors":"<color>+|none","-moz-border-left-colors":"<color>+|none","-moz-border-right-colors":"<color>+|none","-moz-border-top-colors":"<color>+|none","-moz-context-properties":"none|[fill|fill-opacity|stroke|stroke-opacity]#","-moz-float-edge":"border-box|content-box|margin-box|padding-box","-moz-force-broken-image-icon":"0|1","-moz-image-region":"<shape>|auto","-moz-orient":"inline|block|horizontal|vertical","-moz-outline-radius":"<outline-radius>{1,4} [/ <outline-radius>{1,4}]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore|stretch-to-fit","-moz-text-blink":"none|blink","-moz-user-focus":"ignore|normal|select-after|select-before|select-menu|select-same|select-all|none","-moz-user-input":"auto|none|enabled|disabled","-moz-user-modify":"read-only|read-write|write-only","-moz-window-dragging":"drag|no-drag","-moz-window-shadow":"default|menu|tooltip|sheet|none","-webkit-appearance":"none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|inner-spin-button|listbox|listitem|media-controls-background|media-controls-fullscreen-background|media-current-time-display|media-enter-fullscreen-button|media-exit-fullscreen-button|media-fullscreen-button|media-mute-button|media-overlay-play-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|media-time-remaining-display|media-toggle-closed-captions-button|media-volume-slider|media-volume-slider-container|media-volume-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|meter|progress-bar|progress-bar-value|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield|-apple-pay-button","-webkit-border-before":"<'border-width'>||<'border-style'>||<color>","-webkit-border-before-color":"<color>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[above|below|right|left]? <length>? <image>?","-webkit-line-clamp":"none|<integer>","-webkit-mask":"[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"[<box>|border|padding|content|text]#","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[<box>|border|padding|content]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[<length-percentage>|left|center|right]#","-webkit-mask-position-y":"[<length-percentage>|top|center|bottom]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat|no-repeat|space|round","-webkit-mask-repeat-y":"repeat|no-repeat|space|round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto|touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length>||<color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default|none","-webkit-user-modify":"read-only|read-write|read-write-plaintext-only","accent-color":"auto|<color>","align-content":"normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>","align-items":"normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]","align-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>","align-tracks":"[normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>]#",all:"initial|inherit|unset|revert|revert-layer",animation:"<single-animation>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[none|<keyframes-name>]#","animation-play-state":"<single-animation-play-state>#","animation-timing-function":"<easing-function>#","animation-timeline":"<single-animation-timeline>#",appearance:"none|auto|textfield|menulist-button|<compat-auto>","aspect-ratio":"auto|<ratio>",azimuth:"<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards","backdrop-filter":"none|<filter-function-list>","backface-visibility":"visible|hidden",background:"[<bg-layer> ,]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<bg-clip>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[center|[[left|right|x-start|x-end]? <length-percentage>?]!]#","background-position-y":"[center|[[top|bottom|y-start|y-end]? <length-percentage>?]!]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-overflow":"clip|ellipsis|<string>","block-size":"<'width'>",border:"<line-width>||<line-style>||<color>","border-block":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-color":"<'border-top-color'>{1,2}","border-block-style":"<'border-top-style'>","border-block-width":"<'border-top-width'>","border-block-end":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-end-color":"<'border-top-color'>","border-block-end-style":"<'border-top-style'>","border-block-end-width":"<'border-top-width'>","border-block-start":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-start-color":"<'border-top-color'>","border-block-start-style":"<'border-top-style'>","border-block-start-width":"<'border-top-width'>","border-bottom":"<line-width>||<line-style>||<color>","border-bottom-color":"<'border-top-color'>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<line-style>","border-bottom-width":"<line-width>","border-collapse":"collapse|separate","border-color":"<color>{1,4}","border-end-end-radius":"<length-percentage>{1,2}","border-end-start-radius":"<length-percentage>{1,2}","border-image":"<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>","border-image-outset":"[<length>|<number>]{1,4}","border-image-repeat":"[stretch|repeat|round|space]{1,2}","border-image-slice":"<number-percentage>{1,4}&&fill?","border-image-source":"none|<image>","border-image-width":"[<length-percentage>|<number>|auto]{1,4}","border-inline":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-end":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-color":"<'border-top-color'>{1,2}","border-inline-style":"<'border-top-style'>","border-inline-width":"<'border-top-width'>","border-inline-end-color":"<'border-top-color'>","border-inline-end-style":"<'border-top-style'>","border-inline-end-width":"<'border-top-width'>","border-inline-start":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-start-color":"<'border-top-color'>","border-inline-start-style":"<'border-top-style'>","border-inline-start-width":"<'border-top-width'>","border-left":"<line-width>||<line-style>||<color>","border-left-color":"<color>","border-left-style":"<line-style>","border-left-width":"<line-width>","border-radius":"<length-percentage>{1,4} [/ <length-percentage>{1,4}]?","border-right":"<line-width>||<line-style>||<color>","border-right-color":"<color>","border-right-style":"<line-style>","border-right-width":"<line-width>","border-spacing":"<length> <length>?","border-start-end-radius":"<length-percentage>{1,2}","border-start-start-radius":"<length-percentage>{1,2}","border-style":"<line-style>{1,4}","border-top":"<line-width>||<line-style>||<color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<line-style>","border-top-width":"<line-width>","border-width":"<line-width>{1,4}",bottom:"<length>|<percentage>|auto","box-align":"start|center|end|baseline|stretch","box-decoration-break":"slice|clone","box-direction":"normal|reverse|inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single|multiple","box-ordinal-group":"<integer>","box-orient":"horizontal|vertical|inline-axis|block-axis|inherit","box-pack":"start|center|end|justify","box-shadow":"none|<shadow>#","box-sizing":"content-box|border-box","break-after":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-before":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-inside":"auto|avoid|avoid-page|avoid-column|avoid-region","caption-side":"top|bottom|block-start|block-end|inline-start|inline-end","caret-color":"auto|<color>",clear:"none|left|right|both|inline-start|inline-end",clip:"<shape>|auto","clip-path":"<clip-source>|[<basic-shape>||<geometry-box>]|none",color:"<color>","print-color-adjust":"economy|exact","color-scheme":"normal|[light|dark|<custom-ident>]+&&only?","column-count":"<integer>|auto","column-fill":"auto|balance|balance-all","column-gap":"normal|<length-percentage>","column-rule":"<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none|all","column-width":"<length>|auto",columns:"<'column-width'>||<'column-count'>",contain:"none|strict|content|[size||layout||style||paint]",content:"normal|none|[<content-replacement>|<content-list>] [/ [<string>|<counter>]+]?","content-visibility":"visible|auto|hidden","counter-increment":"[<counter-name> <integer>?]+|none","counter-reset":"[<counter-name> <integer>?|<reversed-counter-name> <integer>?]+|none","counter-set":"[<counter-name> <integer>?]+|none",cursor:"[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]",direction:"ltr|rtl",display:"[<display-outside>||<display-inside>]|<display-listitem>|<display-internal>|<display-box>|<display-legacy>|<-non-standard-display>","empty-cells":"show|hide",filter:"none|<filter-function-list>|<-ms-filter-function-list>",flex:"none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]","flex-basis":"content|<'width'>","flex-direction":"row|row-reverse|column|column-reverse","flex-flow":"<'flex-direction'>||<'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap|wrap|wrap-reverse",float:"left|right|none|inline-start|inline-end",font:"[[<'font-style'>||<font-variant-css21>||<'font-weight'>||<'font-stretch'>]? <'font-size'> [/ <'line-height'>]? <'font-family'>]|caption|icon|menu|message-box|small-caption|status-bar","font-family":"[<family-name>|<generic-family>]#","font-feature-settings":"normal|<feature-tag-value>#","font-kerning":"auto|normal|none","font-language-override":"normal|<string>","font-optical-sizing":"auto|none","font-variation-settings":"normal|[<string> <number>]#","font-size":"<absolute-size>|<relative-size>|<length-percentage>","font-size-adjust":"none|[ex-height|cap-height|ch-width|ic-width|ic-height]? [from-font|<number>]","font-smooth":"auto|never|always|<absolute-size>|<length>","font-stretch":"<font-stretch-absolute>","font-style":"normal|italic|oblique <angle>?","font-synthesis":"none|[weight||style||small-caps]","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-alternates":"normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]","font-variant-caps":"normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","font-variant-east-asian":"normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-ligatures":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]","font-variant-numeric":"normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]","font-variant-position":"normal|sub|super","font-weight":"<font-weight-absolute>|bolder|lighter","forced-color-adjust":"auto|none",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [/ <grid-line>]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[row|column]||dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [/ <grid-line>]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [/ <grid-line>]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?","grid-template-areas":"none|<string>+","grid-template-columns":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","grid-template-rows":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","hanging-punctuation":"none|[first||[force-end|allow-end]||last]",height:"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )","hyphenate-character":"auto|<string>",hyphens:"none|manual|auto","image-orientation":"from-image|<angle>|[<angle>? flip]","image-rendering":"auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>","image-resolution":"[from-image||<resolution>]&&snap?","ime-mode":"auto|normal|active|inactive|disabled","initial-letter":"normal|[<number> <integer>?]","initial-letter-align":"[auto|alphabetic|hanging|ideographic]","inline-size":"<'width'>","input-security":"auto|none",inset:"<'top'>{1,4}","inset-block":"<'top'>{1,2}","inset-block-end":"<'top'>","inset-block-start":"<'top'>","inset-inline":"<'top'>{1,2}","inset-inline-end":"<'top'>","inset-inline-start":"<'top'>",isolation:"auto|isolate","justify-content":"normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]","justify-items":"normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]","justify-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]","justify-tracks":"[normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]]#",left:"<length>|<percentage>|auto","letter-spacing":"normal|<length-percentage>","line-break":"auto|loose|normal|strict|anywhere","line-clamp":"none|<integer>","line-height":"normal|<number>|<length>|<percentage>","line-height-step":"<length>","list-style":"<'list-style-type'>||<'list-style-position'>||<'list-style-image'>","list-style-image":"<image>|none","list-style-position":"inside|outside","list-style-type":"<counter-style>|<string>|none",margin:"[<length>|<percentage>|auto]{1,4}","margin-block":"<'margin-left'>{1,2}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length>|<percentage>|auto","margin-inline":"<'margin-left'>{1,2}","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length>|<percentage>|auto","margin-right":"<length>|<percentage>|auto","margin-top":"<length>|<percentage>|auto","margin-trim":"none|in-flow|all",mask:"<mask-layer>#","mask-border":"<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>","mask-border-mode":"luminance|alpha","mask-border-outset":"[<length>|<number>]{1,4}","mask-border-repeat":"[stretch|repeat|round|space]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none|<image>","mask-border-width":"[<length-percentage>|<number>|auto]{1,4}","mask-clip":"[<geometry-box>|no-clip]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance|alpha","masonry-auto-flow":"[pack|next]||[definite-first|ordered]","math-style":"normal|compact","max-block-size":"<'max-width'>","max-height":"none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )","max-inline-size":"<'max-width'>","max-lines":"none|<integer>","max-width":"none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|<-non-standard-width>","min-block-size":"<'min-width'>","min-height":"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )","min-inline-size":"<'min-width'>","min-width":"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|<-non-standard-width>","mix-blend-mode":"<blend-mode>|plus-lighter","object-fit":"fill|contain|cover|none|scale-down","object-position":"<position>",offset:"[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?","offset-anchor":"auto|<position>","offset-distance":"<length-percentage>","offset-path":"none|ray( [<angle>&&<size>&&contain?] )|<path()>|<url>|[<basic-shape>||<geometry-box>]","offset-position":"auto|<position>","offset-rotate":"[auto|reverse]||<angle>",opacity:"<alpha-value>",order:"<integer>",orphans:"<integer>",outline:"[<'outline-color'>||<'outline-style'>||<'outline-width'>]","outline-color":"<color>|invert","outline-offset":"<length>","outline-style":"auto|<'border-style'>","outline-width":"<line-width>",overflow:"[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>","overflow-anchor":"auto|none","overflow-block":"visible|hidden|clip|scroll|auto","overflow-clip-box":"padding-box|content-box","overflow-clip-margin":"<visual-box>||<length [0,\u221E]>","overflow-inline":"visible|hidden|clip|scroll|auto","overflow-wrap":"normal|break-word|anywhere","overflow-x":"visible|hidden|clip|scroll|auto","overflow-y":"visible|hidden|clip|scroll|auto","overscroll-behavior":"[contain|none|auto]{1,2}","overscroll-behavior-block":"contain|none|auto","overscroll-behavior-inline":"contain|none|auto","overscroll-behavior-x":"contain|none|auto","overscroll-behavior-y":"contain|none|auto",padding:"[<length>|<percentage>]{1,4}","padding-block":"<'padding-left'>{1,2}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length>|<percentage>","padding-inline":"<'padding-left'>{1,2}","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length>|<percentage>","padding-right":"<length>|<percentage>","padding-top":"<length>|<percentage>","page-break-after":"auto|always|avoid|left|right|recto|verso","page-break-before":"auto|always|avoid|left|right|recto|verso","page-break-inside":"auto|avoid","paint-order":"normal|[fill||stroke||markers]",perspective:"none|<length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","place-items":"<'align-items'> <'justify-items'>?","place-self":"<'align-self'> <'justify-self'>?","pointer-events":"auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit",position:"static|relative|absolute|sticky|fixed|-webkit-sticky",quotes:"none|auto|[<string> <string>]+",resize:"none|both|horizontal|vertical|block|inline",right:"<length>|<percentage>|auto",rotate:"none|<angle>|[x|y|z|<number>{3}]&&<angle>","row-gap":"normal|<length-percentage>","ruby-align":"start|center|space-between|space-around","ruby-merge":"separate|collapse|auto","ruby-position":"[alternate||[over|under]]|inter-character",scale:"none|<number>{1,3}","scrollbar-color":"auto|<color>{2}","scrollbar-gutter":"auto|stable&&both-edges?","scrollbar-width":"auto|thin|none","scroll-behavior":"auto|smooth","scroll-margin":"<length>{1,4}","scroll-margin-block":"<length>{1,2}","scroll-margin-block-start":"<length>","scroll-margin-block-end":"<length>","scroll-margin-bottom":"<length>","scroll-margin-inline":"<length>{1,2}","scroll-margin-inline-start":"<length>","scroll-margin-inline-end":"<length>","scroll-margin-left":"<length>","scroll-margin-right":"<length>","scroll-margin-top":"<length>","scroll-padding":"[auto|<length-percentage>]{1,4}","scroll-padding-block":"[auto|<length-percentage>]{1,2}","scroll-padding-block-start":"auto|<length-percentage>","scroll-padding-block-end":"auto|<length-percentage>","scroll-padding-bottom":"auto|<length-percentage>","scroll-padding-inline":"[auto|<length-percentage>]{1,2}","scroll-padding-inline-start":"auto|<length-percentage>","scroll-padding-inline-end":"auto|<length-percentage>","scroll-padding-left":"auto|<length-percentage>","scroll-padding-right":"auto|<length-percentage>","scroll-padding-top":"auto|<length-percentage>","scroll-snap-align":"[none|start|end|center]{1,2}","scroll-snap-coordinate":"none|<position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none|repeat( <length-percentage> )","scroll-snap-points-y":"none|repeat( <length-percentage> )","scroll-snap-stop":"normal|always","scroll-snap-type":"none|[x|y|block|inline|both] [mandatory|proximity]?","scroll-snap-type-x":"none|mandatory|proximity","scroll-snap-type-y":"none|mandatory|proximity","shape-image-threshold":"<alpha-value>","shape-margin":"<length-percentage>","shape-outside":"none|[<shape-box>||<basic-shape>]|<image>","tab-size":"<integer>|<length>","table-layout":"auto|fixed","text-align":"start|end|left|right|center|justify|match-parent","text-align-last":"auto|start|end|left|right|center|justify","text-combine-upright":"none|all|[digits <integer>?]","text-decoration":"<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>||<'text-decoration-thickness'>","text-decoration-color":"<color>","text-decoration-line":"none|[underline||overline||line-through||blink]|spelling-error|grammar-error","text-decoration-skip":"none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]","text-decoration-skip-ink":"auto|all|none","text-decoration-style":"solid|double|dotted|dashed|wavy","text-decoration-thickness":"auto|from-font|<length>|<percentage>","text-emphasis":"<'text-emphasis-style'>||<'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"[over|under]&&[right|left]","text-emphasis-style":"none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>","text-indent":"<length-percentage>&&hanging?&&each-line?","text-justify":"auto|inter-character|inter-word|none","text-orientation":"mixed|upright|sideways","text-overflow":"[clip|ellipsis|<string>]{1,2}","text-rendering":"auto|optimizeSpeed|optimizeLegibility|geometricPrecision","text-shadow":"none|<shadow-t>#","text-size-adjust":"none|auto|<percentage>","text-transform":"none|capitalize|uppercase|lowercase|full-width|full-size-kana","text-underline-offset":"auto|<length>|<percentage>","text-underline-position":"auto|from-font|[under||[left|right]]",top:"<length>|<percentage>|auto","touch-action":"auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation",transform:"none|<transform-list>","transform-box":"content-box|border-box|fill-box|stroke-box|view-box","transform-origin":"[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?","transform-style":"flat|preserve-3d",transition:"<single-transition>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none|<single-transition-property>#","transition-timing-function":"<easing-function>#",translate:"none|<length-percentage> [<length-percentage> <length>?]?","unicode-bidi":"normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate|-webkit-isolate-override|-webkit-plaintext","user-select":"auto|text|none|contain|all","vertical-align":"baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>",visibility:"visible|hidden|collapse","white-space":"normal|pre|nowrap|pre-wrap|pre-line|break-spaces",widows:"<integer>",width:"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|fill|stretch|intrinsic|-moz-max-content|-webkit-max-content|-moz-fit-content|-webkit-fit-content","will-change":"auto|<animateable-feature>#","word-break":"normal|break-all|keep-all|break-word","word-spacing":"normal|<length>","word-wrap":"normal|break-word","writing-mode":"horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>","z-index":"auto|<integer>",zoom:"normal|reset|<number>|<percentage>","-moz-background-clip":"padding|border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-control-character-visibility":"visible|hidden","-moz-osx-font-smoothing":"auto|grayscale","-moz-user-select":"none|text|all|-moz-none","-ms-flex-align":"start|end|center|baseline|stretch","-ms-flex-item-align":"auto|start|end|center|baseline|stretch","-ms-flex-line-pack":"start|end|center|justify|distribute|stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start|end|center|justify|distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor|bicubic","-ms-grid-column-align":"start|end|center|stretch","-ms-grid-row-align":"start|end|center|stretch","-ms-hyphenate-limit-last":"none|always|column|page|spread","-webkit-background-clip":"[<box>|border|padding|content|text]#","-webkit-column-break-after":"always|auto|avoid","-webkit-column-break-before":"always|auto|avoid","-webkit-column-break-inside":"always|auto|avoid","-webkit-font-smoothing":"auto|none|antialiased|subpixel-antialiased","-webkit-mask-box-image":"[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?","-webkit-print-color-adjust":"economy|exact","-webkit-text-security":"none|circle|disc|square","-webkit-user-drag":"none|element|auto","-webkit-user-select":"auto|none|text|all","alignment-baseline":"auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical","baseline-shift":"baseline|sub|super|<svg-length>",behavior:"<url>+","clip-rule":"nonzero|evenodd",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>?|none","cue-before":"<url> <decibel>?|none","dominant-baseline":"auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero|evenodd","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto|<svg-length>",marker:"none|<url>","marker-end":"none|<url>","marker-mid":"none|<url>","marker-start":"none|<url>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time>|none|x-weak|weak|medium|strong|x-strong","pause-before":"<time>|none|x-weak|weak|medium|strong|x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time>|none|x-weak|weak|medium|strong|x-strong","rest-before":"<time>|none|x-weak|weak|medium|strong|x-strong","shape-rendering":"auto|optimizeSpeed|crispEdges|geometricPrecision",src:"[<url> [format( <string># )]?|local( <family-name> )]#",speak:"auto|none|normal","speak-as":"normal|spell-out||digits||[literal-punctuation|no-punctuation]",stroke:"<paint>","stroke-dasharray":"none|[<svg-length>+]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt|round|square","stroke-linejoin":"miter|round|bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<number-zero-one>","stroke-width":"<svg-length>","text-anchor":"start|middle|end","unicode-range":"<urange>#","voice-balance":"<number>|left|center|right|leftwards|rightwards","voice-duration":"auto|<time>","voice-family":"[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve","voice-pitch":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-range":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-rate":"[normal|x-slow|slow|medium|fast|x-fast]||<percentage>","voice-stress":"normal|strong|moderate|none|reduced","voice-volume":"silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]"},atrules:{charset:{prelude:"<string>",descriptors:null},"counter-style":{prelude:"<counter-style-name>",descriptors:{"additive-symbols":"[<integer>&&<symbol>]#",fallback:"<counter-style-name>",negative:"<symbol> <symbol>?",pad:"<integer>&&<symbol>",prefix:"<symbol>",range:"[[<integer>|infinite]{2}]#|auto","speak-as":"auto|bullets|numbers|words|spell-out|<counter-style-name>",suffix:"<symbol>",symbols:"<symbol>+",system:"cyclic|numeric|alphabetic|symbolic|additive|[fixed <integer>?]|[extends <counter-style-name>]"}},document:{prelude:"[<url>|url-prefix( <string> )|domain( <string> )|media-document( <string> )|regexp( <string> )]#",descriptors:null},"font-face":{prelude:null,descriptors:{"ascent-override":"normal|<percentage>","descent-override":"normal|<percentage>","font-display":"[auto|block|swap|fallback|optional]","font-family":"<family-name>","font-feature-settings":"normal|<feature-tag-value>#","font-variation-settings":"normal|[<string> <number>]#","font-stretch":"<font-stretch-absolute>{1,2}","font-style":"normal|italic|oblique <angle>{0,2}","font-weight":"<font-weight-absolute>{1,2}","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","line-gap-override":"normal|<percentage>","size-adjust":"<percentage>",src:"[<url> [format( <string># )]?|local( <family-name> )]#","unicode-range":"<urange>#"}},"font-feature-values":{prelude:"<family-name>#",descriptors:null},import:{prelude:"[<string>|<url>] [layer|layer( <layer-name> )]? [supports( [<supports-condition>|<declaration>] )]? <media-query-list>?",descriptors:null},keyframes:{prelude:"<keyframes-name>",descriptors:null},layer:{prelude:"[<layer-name>#|<layer-name>?]",descriptors:null},media:{prelude:"<media-query-list>",descriptors:null},namespace:{prelude:"<namespace-prefix>? [<string>|<url>]",descriptors:null},page:{prelude:"<page-selector-list>",descriptors:{bleed:"auto|<length>",marks:"none|[crop||cross]",size:"<length>{1,2}|auto|[<page-size>||[portrait|landscape]]"}},property:{prelude:"<custom-property-name>",descriptors:{syntax:"<string>",inherits:"true|false","initial-value":"<string>"}},"scroll-timeline":{prelude:"<timeline-name>",descriptors:null},supports:{prelude:"<supports-condition>",descriptors:null},viewport:{prelude:null,descriptors:{height:"<viewport-length>{1,2}","max-height":"<viewport-length>","max-width":"<viewport-length>","max-zoom":"auto|<number>|<percentage>","min-height":"<viewport-length>","min-width":"<viewport-length>","min-zoom":"auto|<number>|<percentage>",orientation:"auto|portrait|landscape","user-zoom":"zoom|fixed","viewport-fit":"auto|contain|cover",width:"<viewport-length>{1,2}",zoom:"auto|<number>|<percentage>"}}}};var It={};y(It,{AnPlusB:()=>kn,Atrule:()=>vn,AtrulePrelude:()=>Cn,AttributeSelector:()=>Tn,Block:()=>Pn,Brackets:()=>On,CDC:()=>Nn,CDO:()=>Rn,ClassSelector:()=>Fn,Combinator:()=>_n,Comment:()=>jn,Declaration:()=>Hn,DeclarationList:()=>Yn,Dimension:()=>Gn,Function:()=>Qn,Hash:()=>Xn,IdSelector:()=>to,Identifier:()=>Jn,MediaFeature:()=>no,MediaQuery:()=>io,MediaQueryList:()=>so,Nth:()=>co,Number:()=>po,Operator:()=>fo,Parentheses:()=>go,Percentage:()=>yo,PseudoClassSelector:()=>ko,PseudoElementSelector:()=>vo,Ratio:()=>Co,Raw:()=>Eo,Rule:()=>Lo,Selector:()=>Io,SelectorList:()=>Do,String:()=>zo,StyleSheet:()=>Bo,TypeSelector:()=>jo,UnicodeRange:()=>Wo,Url:()=>Qo,Value:()=>Xo,WhiteSpace:()=>Jo});var kn={};y(kn,{generate:()=>Ju,name:()=>Xu,parse:()=>xn,structure:()=>Zu});var be=43,ie=45,mr=110,Fe=!0,$u=!1;function dr(e,t){let r=this.tokenStart+e,n=this.charCodeAt(r);for((n===be||n===ie)&&(t&&this.error("Number sign is not allowed"),r++);r<this.tokenEnd;r++)j(this.charCodeAt(r))||this.error("Integer is expected",r)}function rt(e){return dr.call(this,0,e)}function Ie(e,t){if(!this.cmpChar(this.tokenStart+e,t)){let r="";switch(t){case mr:r="N is expected";break;case ie:r="HyphenMinus is expected";break}this.error(r,this.tokenStart+e)}}function yn(){let e=0,t=0,r=this.tokenType;for(;r===13||r===25;)r=this.lookupType(++e);if(r!==10)if(this.isDelim(be,e)||this.isDelim(ie,e)){t=this.isDelim(be,e)?be:ie;do r=this.lookupType(++e);while(r===13||r===25);r!==10&&(this.skip(e),rt.call(this,Fe))}else return null;return e>0&&this.skip(e),t===0&&(r=this.charCodeAt(this.tokenStart),r!==be&&r!==ie&&this.error("Number sign is expected")),rt.call(this,t!==0),t===ie?"-"+this.consume(10):this.consume(10)}var Xu="AnPlusB",Zu={a:[String,null],b:[String,null]};function xn(){let e=this.tokenStart,t=null,r=null;if(this.tokenType===10)rt.call(this,$u),r=this.consume(10);else if(this.tokenType===1&&this.cmpChar(this.tokenStart,ie))switch(t="-1",Ie.call(this,1,mr),this.tokenEnd-this.tokenStart){case 2:this.next(),r=yn.call(this);break;case 3:Ie.call(this,2,ie),this.next(),this.skipSC(),rt.call(this,Fe),r="-"+this.consume(10);break;default:Ie.call(this,2,ie),dr.call(this,3,Fe),this.next(),r=this.substrToCursor(e+2)}else if(this.tokenType===1||this.isDelim(be)&&this.lookupType(1)===1){let n=0;switch(t="1",this.isDelim(be)&&(n=1,this.next()),Ie.call(this,0,mr),this.tokenEnd-this.tokenStart){case 1:this.next(),r=yn.call(this);break;case 2:Ie.call(this,1,ie),this.next(),this.skipSC(),rt.call(this,Fe),r="-"+this.consume(10);break;default:Ie.call(this,1,ie),dr.call(this,2,Fe),this.next(),r=this.substrToCursor(e+n+1)}}else if(this.tokenType===12){let n=this.charCodeAt(this.tokenStart),o=n===be||n===ie,i=this.tokenStart+o;for(;i<this.tokenEnd&&j(this.charCodeAt(i));i++);i===this.tokenStart+o&&this.error("Integer is expected",this.tokenStart+o),Ie.call(this,i-this.tokenStart,mr),t=this.substring(e,i),i+1===this.tokenEnd?(this.next(),r=yn.call(this)):(Ie.call(this,i-this.tokenStart+1,ie),i+2===this.tokenEnd?(this.next(),this.skipSC(),rt.call(this,Fe),r="-"+this.consume(10)):(dr.call(this,i-this.tokenStart+2,Fe),this.next(),r=this.substrToCursor(i+1)))}else this.error();return t!==null&&t.charCodeAt(0)===be&&(t=t.substr(1)),r!==null&&r.charCodeAt(0)===be&&(r=r.substr(1)),{type:"AnPlusB",loc:this.getLocation(e,this.tokenStart),a:t,b:r}}function Ju(e){if(e.a){let t=e.a==="+1"&&"n"||e.a==="1"&&"n"||e.a==="-1"&&"-n"||e.a+"n";if(e.b){let r=e.b[0]==="-"||e.b[0]==="+"?e.b:"+"+e.b;this.tokenize(t+r)}else this.tokenize(t)}else this.tokenize(e.b)}var vn={};y(vn,{generate:()=>op,name:()=>tp,parse:()=>wn,structure:()=>np,walkContext:()=>rp});function gs(e){return this.Raw(e,this.consumeUntilLeftCurlyBracketOrSemicolon,!0)}function ep(){for(let e=1,t;t=this.lookupType(e);e++){if(t===24)return!0;if(t===23||t===3)return!1}return!1}var tp="Atrule",rp="atrule",np={name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]};function wn(){let e=this.tokenStart,t,r,n=null,o=null;switch(this.eat(3),t=this.substrToCursor(e+1),r=t.toLowerCase(),this.skipSC(),this.eof===!1&&this.tokenType!==23&&this.tokenType!==17&&(this.parseAtrulePrelude?n=this.parseWithFallback(this.AtrulePrelude.bind(this,t),gs):n=gs.call(this,this.tokenIndex),this.skipSC()),this.tokenType){case 17:this.next();break;case 23:hasOwnProperty.call(this.atrule,r)&&typeof this.atrule[r].block=="function"?o=this.atrule[r].block.call(this):o=this.Block(ep.call(this));break}return{type:"Atrule",loc:this.getLocation(e,this.tokenStart),name:t,prelude:n,block:o}}function op(e){this.token(3,"@"+e.name),e.prelude!==null&&this.node(e.prelude),e.block?this.node(e.block):this.token(17,";")}var Cn={};y(Cn,{generate:()=>lp,name:()=>ip,parse:()=>Sn,structure:()=>sp,walkContext:()=>ap});var ip="AtrulePrelude",ap="atrulePrelude",sp={children:[[]]};function Sn(e){let t=null;return e!==null&&(e=e.toLowerCase()),this.skipSC(),hasOwnProperty.call(this.atrule,e)&&typeof this.atrule[e].prelude=="function"?t=this.atrule[e].prelude.call(this):t=this.readSequence(this.scope.AtrulePrelude),this.skipSC(),this.eof!==!0&&this.tokenType!==23&&this.tokenType!==17&&this.error("Semicolon or block is expected"),{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}}function lp(e){this.children(e)}var Tn={};y(Tn,{generate:()=>gp,name:()=>mp,parse:()=>En,structure:()=>dp});var cp=36,bs=42,gr=61,up=94,An=124,pp=126;function hp(){this.eof&&this.error("Unexpected end of input");let e=this.tokenStart,t=!1;return this.isDelim(bs)?(t=!0,this.next()):this.isDelim(An)||this.eat(1),this.isDelim(An)?this.charCodeAt(this.tokenStart+1)!==gr?(this.next(),this.eat(1)):t&&this.error("Identifier is expected",this.tokenEnd):t&&this.error("Vertical line is expected"),{type:"Identifier",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e)}}function fp(){let e=this.tokenStart,t=this.charCodeAt(e);return t!==gr&&t!==pp&&t!==up&&t!==cp&&t!==bs&&t!==An&&this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),this.next(),t!==gr&&(this.isDelim(gr)||this.error("Equal sign is expected"),this.next()),this.substrToCursor(e)}var mp="AttributeSelector",dp={name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]};function En(){let e=this.tokenStart,t,r=null,n=null,o=null;return this.eat(19),this.skipSC(),t=hp.call(this),this.skipSC(),this.tokenType!==20&&(this.tokenType!==1&&(r=fp.call(this),this.skipSC(),n=this.tokenType===5?this.String():this.Identifier(),this.skipSC()),this.tokenType===1&&(o=this.consume(1),this.skipSC())),this.eat(20),{type:"AttributeSelector",loc:this.getLocation(e,this.tokenStart),name:t,matcher:r,value:n,flags:o}}function gp(e){this.token(9,"["),this.node(e.name),e.matcher!==null&&(this.tokenize(e.matcher),this.node(e.value)),e.flags!==null&&this.token(1,e.flags),this.token(9,"]")}var Pn={};y(Pn,{generate:()=>vp,name:()=>xp,parse:()=>Ln,structure:()=>wp,walkContext:()=>kp});function xs(e){return this.Raw(e,null,!0)}function bp(){return this.parseWithFallback(this.Rule,xs)}function ys(e){return this.Raw(e,this.consumeUntilSemicolonIncluded,!0)}function yp(){if(this.tokenType===17)return ys.call(this,this.tokenIndex);let e=this.parseWithFallback(this.Declaration,ys);return this.tokenType===17&&this.next(),e}var xp="Block",kp="block",wp={children:[["Atrule","Rule","Declaration"]]};function Ln(e){let t=e?yp:bp,r=this.tokenStart,n=this.createList();this.eat(23);e:for(;!this.eof;)switch(this.tokenType){case 24:break e;case 13:case 25:this.next();break;case 3:n.push(this.parseWithFallback(this.Atrule,xs));break;default:n.push(t.call(this))}return this.eof||this.eat(24),{type:"Block",loc:this.getLocation(r,this.tokenStart),children:n}}function vp(e){this.token(23,"{"),this.children(e,t=>{t.type==="Declaration"&&this.token(17,";")}),this.token(24,"}")}var On={};y(On,{generate:()=>Ap,name:()=>Sp,parse:()=>In,structure:()=>Cp});var Sp="Brackets",Cp={children:[[]]};function In(e,t){let r=this.tokenStart,n=null;return this.eat(19),n=e.call(this,t),this.eof||this.eat(20),{type:"Brackets",loc:this.getLocation(r,this.tokenStart),children:n}}function Ap(e){this.token(9,"["),this.children(e),this.token(9,"]")}var Nn={};y(Nn,{generate:()=>Lp,name:()=>Ep,parse:()=>Dn,structure:()=>Tp});var Ep="CDC",Tp=[];function Dn(){let e=this.tokenStart;return this.eat(15),{type:"CDC",loc:this.getLocation(e,this.tokenStart)}}function Lp(){this.token(15,"-->")}var Rn={};y(Rn,{generate:()=>Op,name:()=>Pp,parse:()=>Mn,structure:()=>Ip});var Pp="CDO",Ip=[];function Mn(){let e=this.tokenStart;return this.eat(14),{type:"CDO",loc:this.getLocation(e,this.tokenStart)}}function Op(){this.token(14,"<!--")}var Fn={};y(Fn,{generate:()=>Rp,name:()=>Np,parse:()=>zn,structure:()=>Mp});var Dp=46,Np="ClassSelector",Mp={name:String};function zn(){return this.eatDelim(Dp),{type:"ClassSelector",loc:this.getLocation(this.tokenStart-1,this.tokenEnd),name:this.consume(1)}}function Rp(e){this.token(9,"."),this.token(1,e.name)}var _n={};y(_n,{generate:()=>jp,name:()=>_p,parse:()=>Bn,structure:()=>Up});var zp=43,ks=47,Fp=62,Bp=126,_p="Combinator",Up={name:String};function Bn(){let e=this.tokenStart,t;switch(this.tokenType){case 13:t=" ";break;case 9:switch(this.charCodeAt(this.tokenStart)){case Fp:case zp:case Bp:this.next();break;case ks:this.next(),this.eatIdent("deep"),this.eatDelim(ks);break;default:this.error("Combinator is expected")}t=this.substrToCursor(e);break}return{type:"Combinator",loc:this.getLocation(e,this.tokenStart),name:t}}function jp(e){this.tokenize(e.name)}var jn={};y(jn,{generate:()=>Vp,name:()=>Wp,parse:()=>Un,structure:()=>Yp});var qp=42,Hp=47,Wp="Comment",Yp={value:String};function Un(){let e=this.tokenStart,t=this.tokenEnd;return this.eat(25),t-e+2>=2&&this.charCodeAt(t-2)===qp&&this.charCodeAt(t-1)===Hp&&(t-=2),{type:"Comment",loc:this.getLocation(e,this.tokenStart),value:this.substring(e+2,t)}}function Vp(e){this.token(25,"/*"+e.value+"*/")}var Hn={};y(Hn,{generate:()=>oh,name:()=>th,parse:()=>qn,structure:()=>nh,walkContext:()=>rh});var vs=33,Gp=35,Kp=36,Qp=38,$p=42,Xp=43,ws=47;function Zp(e){return this.Raw(e,this.consumeUntilExclamationMarkOrSemicolon,!0)}function Jp(e){return this.Raw(e,this.consumeUntilExclamationMarkOrSemicolon,!1)}function eh(){let e=this.tokenIndex,t=this.Value();return t.type!=="Raw"&&this.eof===!1&&this.tokenType!==17&&this.isDelim(vs)===!1&&this.isBalanceEdge(e)===!1&&this.error(),t}var th="Declaration",rh="declaration",nh={important:[Boolean,String],property:String,value:["Value","Raw"]};function qn(){let e=this.tokenStart,t=this.tokenIndex,r=ih.call(this),n=er(r),o=n?this.parseCustomProperty:this.parseValue,i=n?Jp:Zp,a=!1,l;this.skipSC(),this.eat(16);let c=this.tokenIndex;if(n||this.skipSC(),o?l=this.parseWithFallback(eh,i):l=i.call(this,this.tokenIndex),n&&l.type==="Value"&&l.children.isEmpty){for(let s=c-this.tokenIndex;s<=0;s++)if(this.lookupType(s)===13){l.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}}return this.isDelim(vs)&&(a=ah.call(this),this.skipSC()),this.eof===!1&&this.tokenType!==17&&this.isBalanceEdge(t)===!1&&this.error(),{type:"Declaration",loc:this.getLocation(e,this.tokenStart),important:a,property:r,value:l}}function oh(e){this.token(1,e.property),this.token(16,":"),this.node(e.value),e.important&&(this.token(9,"!"),this.token(1,e.important===!0?"important":e.important))}function ih(){let e=this.tokenStart;if(this.tokenType===9)switch(this.charCodeAt(this.tokenStart)){case $p:case Kp:case Xp:case Gp:case Qp:this.next();break;case ws:this.next(),this.isDelim(ws)&&this.next();break}return this.tokenType===4?this.eat(4):this.eat(1),this.substrToCursor(e)}function ah(){this.eat(9),this.skipSC();let e=this.consume(1);return e==="important"?!0:e}var Yn={};y(Yn,{generate:()=>uh,name:()=>lh,parse:()=>Wn,structure:()=>ch});function sh(e){return this.Raw(e,this.consumeUntilSemicolonIncluded,!0)}var lh="DeclarationList",ch={children:[["Declaration"]]};function Wn(){let e=this.createList();e:for(;!this.eof;)switch(this.tokenType){case 13:case 25:case 17:this.next();break;default:e.push(this.parseWithFallback(this.Declaration,sh))}return{type:"DeclarationList",loc:this.getLocationFromList(e),children:e}}function uh(e){this.children(e,t=>{t.type==="Declaration"&&this.token(17,";")})}var Gn={};y(Gn,{generate:()=>fh,name:()=>ph,parse:()=>Vn,structure:()=>hh});var ph="Dimension",hh={value:String,unit:String};function Vn(){let e=this.tokenStart,t=this.consumeNumber(12);return{type:"Dimension",loc:this.getLocation(e,this.tokenStart),value:t,unit:this.substring(e+t.length,this.tokenStart)}}function fh(e){this.token(12,e.value+e.unit)}var Qn={};y(Qn,{generate:()=>bh,name:()=>mh,parse:()=>Kn,structure:()=>gh,walkContext:()=>dh});var mh="Function",dh="function",gh={name:String,children:[[]]};function Kn(e,t){let r=this.tokenStart,n=this.consumeFunctionName(),o=n.toLowerCase(),i;return i=t.hasOwnProperty(o)?t[o].call(this,t):e.call(this,t),this.eof||this.eat(22),{type:"Function",loc:this.getLocation(r,this.tokenStart),name:n,children:i}}function bh(e){this.token(2,e.name+"("),this.children(e),this.token(22,")")}var Xn={};y(Xn,{generate:()=>wh,name:()=>xh,parse:()=>$n,structure:()=>kh,xxx:()=>yh});var yh="XXX",xh="Hash",kh={value:String};function $n(){let e=this.tokenStart;return this.eat(4),{type:"Hash",loc:this.getLocation(e,this.tokenStart),value:this.substrToCursor(e+1)}}function wh(e){this.token(4,"#"+e.value)}var Jn={};y(Jn,{generate:()=>Ch,name:()=>vh,parse:()=>Zn,structure:()=>Sh});var vh="Identifier",Sh={name:String};function Zn(){return{type:"Identifier",loc:this.getLocation(this.tokenStart,this.tokenEnd),name:this.consume(1)}}function Ch(e){this.token(1,e.name)}var to={};y(to,{generate:()=>Th,name:()=>Ah,parse:()=>eo,structure:()=>Eh});var Ah="IdSelector",Eh={name:String};function eo(){let e=this.tokenStart;return this.eat(4),{type:"IdSelector",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e+1)}}function Th(e){this.token(9,"#"+e.name)}var no={};y(no,{generate:()=>Ih,name:()=>Lh,parse:()=>ro,structure:()=>Ph});var Lh="MediaFeature",Ph={name:String,value:["Identifier","Number","Dimension","Ratio",null]};function ro(){let e=this.tokenStart,t,r=null;if(this.eat(21),this.skipSC(),t=this.consume(1),this.skipSC(),this.tokenType!==22){switch(this.eat(16),this.skipSC(),this.tokenType){case 10:this.lookupNonWSType(1)===9?r=this.Ratio():r=this.Number();break;case 12:r=this.Dimension();break;case 1:r=this.Identifier();break;default:this.error("Number, dimension, ratio or identifier is expected")}this.skipSC()}return this.eat(22),{type:"MediaFeature",loc:this.getLocation(e,this.tokenStart),name:t,value:r}}function Ih(e){this.token(21,"("),this.token(1,e.name),e.value!==null&&(this.token(16,":"),this.node(e.value)),this.token(22,")")}var io={};y(io,{generate:()=>Nh,name:()=>Oh,parse:()=>oo,structure:()=>Dh});var Oh="MediaQuery",Dh={children:[["Identifier","MediaFeature","WhiteSpace"]]};function oo(){let e=this.createList(),t=null;this.skipSC();e:for(;!this.eof;){switch(this.tokenType){case 25:case 13:this.next();continue;case 1:t=this.Identifier();break;case 21:t=this.MediaFeature();break;default:break e}e.push(t)}return t===null&&this.error("Identifier or parenthesis is expected"),{type:"MediaQuery",loc:this.getLocationFromList(e),children:e}}function Nh(e){this.children(e)}var so={};y(so,{generate:()=>zh,name:()=>Mh,parse:()=>ao,structure:()=>Rh});var Mh="MediaQueryList",Rh={children:[["MediaQuery"]]};function ao(){let e=this.createList();for(this.skipSC();!this.eof&&(e.push(this.MediaQuery()),this.tokenType===18);)this.next();return{type:"MediaQueryList",loc:this.getLocationFromList(e),children:e}}function zh(e){this.children(e,()=>this.token(18,","))}var co={};y(co,{generate:()=>_h,name:()=>Fh,parse:()=>lo,structure:()=>Bh});var Fh="Nth",Bh={nth:["AnPlusB","Identifier"],selector:["SelectorList",null]};function lo(){this.skipSC();let e=this.tokenStart,t=e,r=null,n;return this.lookupValue(0,"odd")||this.lookupValue(0,"even")?n=this.Identifier():n=this.AnPlusB(),t=this.tokenStart,this.skipSC(),this.lookupValue(0,"of")&&(this.next(),r=this.SelectorList(),t=this.tokenStart),{type:"Nth",loc:this.getLocation(e,t),nth:n,selector:r}}function _h(e){this.node(e.nth),e.selector!==null&&(this.token(1,"of"),this.node(e.selector))}var po={};y(po,{generate:()=>qh,name:()=>Uh,parse:()=>uo,structure:()=>jh});var Uh="Number",jh={value:String};function uo(){return{type:"Number",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consume(10)}}function qh(e){this.token(10,e.value)}var fo={};y(fo,{generate:()=>Yh,name:()=>Hh,parse:()=>ho,structure:()=>Wh});var Hh="Operator",Wh={value:String};function ho(){let e=this.tokenStart;return this.next(),{type:"Operator",loc:this.getLocation(e,this.tokenStart),value:this.substrToCursor(e)}}function Yh(e){this.tokenize(e.value)}var go={};y(go,{generate:()=>Kh,name:()=>Vh,parse:()=>mo,structure:()=>Gh});var Vh="Parentheses",Gh={children:[[]]};function mo(e,t){let r=this.tokenStart,n=null;return this.eat(21),n=e.call(this,t),this.eof||this.eat(22),{type:"Parentheses",loc:this.getLocation(r,this.tokenStart),children:n}}function Kh(e){this.token(21,"("),this.children(e),this.token(22,")")}var yo={};y(yo,{generate:()=>Xh,name:()=>Qh,parse:()=>bo,structure:()=>$h});var Qh="Percentage",$h={value:String};function bo(){return{type:"Percentage",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consumeNumber(11)}}function Xh(e){this.token(11,e.value+"%")}var ko={};y(ko,{generate:()=>tf,name:()=>Zh,parse:()=>xo,structure:()=>ef,walkContext:()=>Jh});var Zh="PseudoClassSelector",Jh="function",ef={name:String,children:[["Raw"],null]};function xo(){let e=this.tokenStart,t=null,r,n;return this.eat(16),this.tokenType===2?(r=this.consumeFunctionName(),n=r.toLowerCase(),hasOwnProperty.call(this.pseudo,n)?(this.skipSC(),t=this.pseudo[n].call(this),this.skipSC()):(t=this.createList(),t.push(this.Raw(this.tokenIndex,null,!1))),this.eat(22)):r=this.consume(1),{type:"PseudoClassSelector",loc:this.getLocation(e,this.tokenStart),name:r,children:t}}function tf(e){this.token(16,":"),e.children===null?this.token(1,e.name):(this.token(2,e.name+"("),this.children(e),this.token(22,")"))}var vo={};y(vo,{generate:()=>af,name:()=>rf,parse:()=>wo,structure:()=>of,walkContext:()=>nf});var rf="PseudoElementSelector",nf="function",of={name:String,children:[["Raw"],null]};function wo(){let e=this.tokenStart,t=null,r,n;return this.eat(16),this.eat(16),this.tokenType===2?(r=this.consumeFunctionName(),n=r.toLowerCase(),hasOwnProperty.call(this.pseudo,n)?(this.skipSC(),t=this.pseudo[n].call(this),this.skipSC()):(t=this.createList(),t.push(this.Raw(this.tokenIndex,null,!1))),this.eat(22)):r=this.consume(1),{type:"PseudoElementSelector",loc:this.getLocation(e,this.tokenStart),name:r,children:t}}function af(e){this.token(16,":"),this.token(16,":"),e.children===null?this.token(1,e.name):(this.token(2,e.name+"("),this.children(e),this.token(22,")"))}var Co={};y(Co,{generate:()=>pf,name:()=>cf,parse:()=>So,structure:()=>uf});var sf=47,lf=46;function Ss(){this.skipSC();let e=this.consume(10);for(let t=0;t<e.length;t++){let r=e.charCodeAt(t);!j(r)&&r!==lf&&this.error("Unsigned number is expected",this.tokenStart-e.length+t)}return Number(e)===0&&this.error("Zero number is not allowed",this.tokenStart-e.length),e}var cf="Ratio",uf={left:String,right:String};function So(){let e=this.tokenStart,t=Ss.call(this),r;return this.skipSC(),this.eatDelim(sf),r=Ss.call(this),{type:"Ratio",loc:this.getLocation(e,this.tokenStart),left:t,right:r}}function pf(e){this.token(10,e.left),this.token(9,"/"),this.token(10,e.right)}var Eo={};y(Eo,{generate:()=>df,name:()=>ff,parse:()=>Ao,structure:()=>mf});function hf(){return this.tokenIndex>0&&this.lookupType(-1)===13?this.tokenIndex>1?this.getTokenStart(this.tokenIndex-1):this.firstCharOffset:this.tokenStart}var ff="Raw",mf={value:String};function Ao(e,t,r){let n=this.getTokenStart(e),o;return this.skipUntilBalanced(e,t||this.consumeUntilBalanceEnd),r&&this.tokenStart>n?o=hf.call(this):o=this.tokenStart,{type:"Raw",loc:this.getLocation(n,o),value:this.substring(n,o)}}function df(e){this.tokenize(e.value)}var Lo={};y(Lo,{generate:()=>kf,name:()=>bf,parse:()=>To,structure:()=>xf,walkContext:()=>yf});function Cs(e){return this.Raw(e,this.consumeUntilLeftCurlyBracket,!0)}function gf(){let e=this.SelectorList();return e.type!=="Raw"&&this.eof===!1&&this.tokenType!==23&&this.error(),e}var bf="Rule",yf="rule",xf={prelude:["SelectorList","Raw"],block:["Block"]};function To(){let e=this.tokenIndex,t=this.tokenStart,r,n;return this.parseRulePrelude?r=this.parseWithFallback(gf,Cs):r=Cs.call(this,e),n=this.Block(!0),{type:"Rule",loc:this.getLocation(t,this.tokenStart),prelude:r,block:n}}function kf(e){this.node(e.prelude),this.node(e.block)}var Io={};y(Io,{generate:()=>Sf,name:()=>wf,parse:()=>Po,structure:()=>vf});var wf="Selector",vf={children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator","WhiteSpace"]]};function Po(){let e=this.readSequence(this.scope.Selector);return this.getFirstListNode(e)===null&&this.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}}function Sf(e){this.children(e)}var Do={};y(Do,{generate:()=>Tf,name:()=>Cf,parse:()=>Oo,structure:()=>Ef,walkContext:()=>Af});var Cf="SelectorList",Af="selector",Ef={children:[["Selector","Raw"]]};function Oo(){let e=this.createList();for(;!this.eof;){if(e.push(this.Selector()),this.tokenType===18){this.next();continue}break}return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}}function Tf(e){this.children(e,()=>this.token(18,","))}var zo={};y(zo,{generate:()=>If,name:()=>Lf,parse:()=>Ro,structure:()=>Pf});var nt={};y(nt,{decode:()=>Tt,encode:()=>Mo});var No=92,As=34,Es=39;function Tt(e){let t=e.length,r=e.charCodeAt(0),n=r===As||r===Es?1:0,o=n===1&&t>1&&e.charCodeAt(t-1)===r?t-2:t-1,i="";for(let a=n;a<=o;a++){let l=e.charCodeAt(a);if(l===No){if(a===o){a!==t-1&&(i=e.substr(a+1));break}if(l=e.charCodeAt(++a),re(No,l)){let c=a-1,s=fe(e,c);a=s-1,i+=ft(e.substring(c+1,s))}else l===13&&e.charCodeAt(a+1)===10&&a++}else i+=e[a]}return i}function Mo(e,t){let r=t?"'":'"',n=t?Es:As,o="",i=!1;for(let a=0;a<e.length;a++){let l=e.charCodeAt(a);if(l===0){o+="\uFFFD";continue}if(l<=31||l===127){o+="\\"+l.toString(16),i=!0;continue}l===n||l===No?(o+="\\"+e.charAt(a),i=!1):(i&&(oe(l)||he(l))&&(o+=" "),o+=e.charAt(a),i=!1)}return r+o+r}var Lf="String",Pf={value:String};function Ro(){return{type:"String",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:Tt(this.consume(5))}}function If(e){this.token(5,Mo(e.value))}var Bo={};y(Bo,{generate:()=>Rf,name:()=>Df,parse:()=>Fo,structure:()=>Mf,walkContext:()=>Nf});var Of=33;function Ts(e){return this.Raw(e,null,!1)}var Df="StyleSheet",Nf="stylesheet",Mf={children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]};function Fo(){let e=this.tokenStart,t=this.createList(),r;e:for(;!this.eof;){switch(this.tokenType){case 13:this.next();continue;case 25:if(this.charCodeAt(this.tokenStart+2)!==Of){this.next();continue}r=this.Comment();break;case 14:r=this.CDO();break;case 15:r=this.CDC();break;case 3:r=this.parseWithFallback(this.Atrule,Ts);break;default:r=this.parseWithFallback(this.Rule,Ts)}t.push(r)}return{type:"StyleSheet",loc:this.getLocation(e,this.tokenStart),children:t}}function Rf(e){this.children(e)}var jo={};y(jo,{generate:()=>_f,name:()=>Ff,parse:()=>Uo,structure:()=>Bf});var zf=42,Ls=124;function _o(){this.tokenType!==1&&this.isDelim(zf)===!1&&this.error("Identifier or asterisk is expected"),this.next()}var Ff="TypeSelector",Bf={name:String};function Uo(){let e=this.tokenStart;return this.isDelim(Ls)?(this.next(),_o.call(this)):(_o.call(this),this.isDelim(Ls)&&(this.next(),_o.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e)}}function _f(e){this.tokenize(e.name)}var Wo={};y(Wo,{generate:()=>Wf,name:()=>qf,parse:()=>Ho,structure:()=>Hf});var Ps=43,Is=45,qo=63;function Lt(e,t){let r=0;for(let n=this.tokenStart+e;n<this.tokenEnd;n++){let o=this.charCodeAt(n);if(o===Is&&t&&r!==0)return Lt.call(this,e+r+1,!1),-1;oe(o)||this.error(t&&r!==0?"Hyphen minus"+(r<6?" or hex digit":"")+" is expected":r<6?"Hex digit is expected":"Unexpected input",n),++r>6&&this.error("Too many hex digits",n)}return this.next(),r}function br(e){let t=0;for(;this.isDelim(qo);)++t>e&&this.error("Too many question marks"),this.next()}function Uf(e){this.charCodeAt(this.tokenStart)!==e&&this.error((e===Ps?"Plus sign":"Hyphen minus")+" is expected")}function jf(){let e=0;switch(this.tokenType){case 10:if(e=Lt.call(this,1,!0),this.isDelim(qo)){br.call(this,6-e);break}if(this.tokenType===12||this.tokenType===10){Uf.call(this,Is),Lt.call(this,1,!1);break}break;case 12:e=Lt.call(this,1,!0),e>0&&br.call(this,6-e);break;default:if(this.eatDelim(Ps),this.tokenType===1){e=Lt.call(this,0,!0),e>0&&br.call(this,6-e);break}if(this.isDelim(qo)){this.next(),br.call(this,5);break}this.error("Hex digit or question mark is expected")}}var qf="UnicodeRange",Hf={value:String};function Ho(){let e=this.tokenStart;return this.eatIdent("u"),jf.call(this),{type:"UnicodeRange",loc:this.getLocation(e,this.tokenStart),value:this.substrToCursor(e)}}function Wf(e){this.tokenize(e.value)}var Qo={};y(Qo,{generate:()=>Xf,name:()=>Qf,parse:()=>Ko,structure:()=>$f});var Pt={};y(Pt,{decode:()=>Vo,encode:()=>Go});var Yf=32,Yo=92,Vf=34,Gf=39,Kf=40,Os=41;function Vo(e){let t=e.length,r=4,n=e.charCodeAt(t-1)===Os?t-2:t-1,o="";for(;r<n&&he(e.charCodeAt(r));)r++;for(;r<n&&he(e.charCodeAt(n));)n--;for(let i=r;i<=n;i++){let a=e.charCodeAt(i);if(a===Yo){if(i===n){i!==t-1&&(o=e.substr(i+1));break}if(a=e.charCodeAt(++i),re(Yo,a)){let l=i-1,c=fe(e,l);i=c-1,o+=ft(e.substring(l+1,c))}else a===13&&e.charCodeAt(i+1)===10&&i++}else o+=e[i]}return o}function Go(e){let t="",r=!1;for(let n=0;n<e.length;n++){let o=e.charCodeAt(n);if(o===0){t+="\uFFFD";continue}if(o<=31||o===127){t+="\\"+o.toString(16),r=!0;continue}o===Yf||o===Yo||o===Vf||o===Gf||o===Kf||o===Os?(t+="\\"+e.charAt(n),r=!1):(r&&oe(o)&&(t+=" "),t+=e.charAt(n),r=!1)}return"url("+t+")"}var Qf="Url",$f={value:String};function Ko(){let e=this.tokenStart,t;switch(this.tokenType){case 7:t=Vo(this.consume(7));break;case 2:this.cmpStr(this.tokenStart,this.tokenEnd,"url(")||this.error("Function name must be `url`"),this.eat(2),this.skipSC(),t=Tt(this.consume(5)),this.skipSC(),this.eof||this.eat(22);break;default:this.error("Url or Function is expected")}return{type:"Url",loc:this.getLocation(e,this.tokenStart),value:t}}function Xf(e){this.token(7,Go(e.value))}var Xo={};y(Xo,{generate:()=>em,name:()=>Zf,parse:()=>$o,structure:()=>Jf});var Zf="Value",Jf={children:[[]]};function $o(){let e=this.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(e,this.tokenStart),children:t}}function em(e){this.children(e)}var Jo={};y(Jo,{generate:()=>om,name:()=>rm,parse:()=>Zo,structure:()=>nm});var tm=Object.freeze({type:"WhiteSpace",loc:null,value:" "}),rm="WhiteSpace",nm={value:String};function Zo(){return this.eat(13),tm}function om(e){this.token(13,e.value)}var Ds={generic:!0,...ds,node:It};var ei={};y(ei,{AtrulePrelude:()=>Ms,Selector:()=>zs,Value:()=>Us});var im=35,am=42,Ns=43,sm=45,lm=47,cm=117;function Ot(e){switch(this.tokenType){case 4:return this.Hash();case 18:return this.Operator();case 21:return this.Parentheses(this.readSequence,e.recognizer);case 19:return this.Brackets(this.readSequence,e.recognizer);case 5:return this.String();case 12:return this.Dimension();case 11:return this.Percentage();case 10:return this.Number();case 2:return this.cmpStr(this.tokenStart,this.tokenEnd,"url(")?this.Url():this.Function(this.readSequence,e.recognizer);case 7:return this.Url();case 1:return this.cmpChar(this.tokenStart,cm)&&this.cmpChar(this.tokenStart+1,Ns)?this.UnicodeRange():this.Identifier();case 9:{let t=this.charCodeAt(this.tokenStart);if(t===lm||t===am||t===Ns||t===sm)return this.Operator();t===im&&this.error("Hex or identifier is expected",this.tokenStart+1);break}}}var Ms={getNode:Ot};var um=35,pm=42,hm=43,fm=47,Rs=46,mm=62,dm=124,gm=126;function bm(e,t){t.last!==null&&t.last.type!=="Combinator"&&e!==null&&e.type!=="Combinator"&&t.push({type:"Combinator",loc:null,name:" "})}function ym(){switch(this.tokenType){case 19:return this.AttributeSelector();case 4:return this.IdSelector();case 16:return this.lookupType(1)===16?this.PseudoElementSelector():this.PseudoClassSelector();case 1:return this.TypeSelector();case 10:case 11:return this.Percentage();case 12:this.charCodeAt(this.tokenStart)===Rs&&this.error("Identifier is expected",this.tokenStart+1);break;case 9:{switch(this.charCodeAt(this.tokenStart)){case hm:case mm:case gm:case fm:return this.Combinator();case Rs:return this.ClassSelector();case pm:case dm:return this.TypeSelector();case um:return this.IdSelector()}break}}}var zs={onWhiteSpace:bm,getNode:ym};function Fs(){return this.createSingleNodeList(this.Raw(this.tokenIndex,null,!1))}function Bs(){let e=this.createList();if(this.skipSC(),e.push(this.Identifier()),this.skipSC(),this.tokenType===18){e.push(this.Operator());let t=this.tokenIndex,r=this.parseCustomProperty?this.Value(null):this.Raw(this.tokenIndex,this.consumeUntilExclamationMarkOrSemicolon,!1);if(r.type==="Value"&&r.children.isEmpty){for(let n=t-this.tokenIndex;n<=0;n++)if(this.lookupType(n)===13){r.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}}e.push(r)}return e}function _s(e){return e!==null&&e.type==="Operator"&&(e.value[e.value.length-1]==="-"||e.value[e.value.length-1]==="+")}var Us={getNode:Ot,onWhiteSpace(e,t){_s(e)&&(e.value=" "+e.value),_s(t.last)&&(t.last.value+=" ")},expression:Fs,var:Bs};var js={parse:{prelude:null,block(){return this.Block(!0)}}};var qs={parse:{prelude(){let e=this.createList();switch(this.skipSC(),this.tokenType){case 5:e.push(this.String());break;case 7:case 2:e.push(this.Url());break;default:this.error("String or url() is expected")}return(this.lookupNonWSType(0)===1||this.lookupNonWSType(0)===21)&&e.push(this.MediaQueryList()),e},block:null}};var Hs={parse:{prelude(){return this.createSingleNodeList(this.MediaQueryList())},block(){return this.Block(!1)}}};var Ws={parse:{prelude(){return this.createSingleNodeList(this.SelectorList())},block(){return this.Block(!0)}}};function xm(){return this.createSingleNodeList(this.Raw(this.tokenIndex,null,!1))}function km(){return this.skipSC(),this.tokenType===1&&this.lookupNonWSType(1)===16?this.createSingleNodeList(this.Declaration()):Ys.call(this)}function Ys(){let e=this.createList(),t;this.skipSC();e:for(;!this.eof;){switch(this.tokenType){case 25:case 13:this.next();continue;case 2:t=this.Function(xm,this.scope.AtrulePrelude);break;case 1:t=this.Identifier();break;case 21:t=this.Parentheses(km,this.scope.AtrulePrelude);break;default:break e}e.push(t)}return e}var Vs={parse:{prelude(){let e=Ys.call(this);return this.getFirstListNode(e)===null&&this.error("Condition is expected"),e},block(){return this.Block(!1)}}};var Gs={"font-face":js,import:qs,media:Hs,page:Ws,supports:Vs};var Be={parse(){return this.createSingleNodeList(this.SelectorList())}},wm={parse(){return this.createSingleNodeList(this.Selector())}},Ks={parse(){return this.createSingleNodeList(this.Identifier())}},yr={parse(){return this.createSingleNodeList(this.Nth())}},Qs={dir:Ks,has:Be,lang:Ks,matches:Be,is:Be,"-moz-any":Be,"-webkit-any":Be,where:Be,not:Be,"nth-child":yr,"nth-last-child":yr,"nth-last-of-type":yr,"nth-of-type":yr,slotted:wm};var ti={};y(ti,{AnPlusB:()=>xn,Atrule:()=>wn,AtrulePrelude:()=>Sn,AttributeSelector:()=>En,Block:()=>Ln,Brackets:()=>In,CDC:()=>Dn,CDO:()=>Mn,ClassSelector:()=>zn,Combinator:()=>Bn,Comment:()=>Un,Declaration:()=>qn,DeclarationList:()=>Wn,Dimension:()=>Vn,Function:()=>Kn,Hash:()=>$n,IdSelector:()=>eo,Identifier:()=>Zn,MediaFeature:()=>ro,MediaQuery:()=>oo,MediaQueryList:()=>ao,Nth:()=>lo,Number:()=>uo,Operator:()=>ho,Parentheses:()=>mo,Percentage:()=>bo,PseudoClassSelector:()=>xo,PseudoElementSelector:()=>wo,Ratio:()=>So,Raw:()=>Ao,Rule:()=>To,Selector:()=>Po,SelectorList:()=>Oo,String:()=>Ro,StyleSheet:()=>Fo,TypeSelector:()=>Uo,UnicodeRange:()=>Ho,Url:()=>Ko,Value:()=>$o,WhiteSpace:()=>Zo});var $s={parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude(e){return this.AtrulePrelude(e.atrule?String(e.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",rule:"Rule",selectorList:"SelectorList",selector:"Selector",block(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},scope:ei,atrule:Gs,pseudo:Qs,node:ti};var Xs={node:It};var Zs=bn({...Ds,...$s,...Xs});function Dt(e){let t={};for(let r in e){let n=e[r];n&&(Array.isArray(n)||n instanceof w?n=n.map(Dt):n.constructor===Object&&(n=Dt(n))),t[r]=n}return t}var{tokenize:Nx,parse:Js,generate:ne,lexer:xr,createLexer:Mx,walk:F,find:Rx,findLast:zx,findAll:Fx,toPlainObject:Bx,fromPlainObject:_x,fork:el}=Zs;var{hasOwnProperty:Sm}=Object.prototype;function ri(e,t){let r=Object.create(null);if(!Array.isArray(e))return null;for(let n of e)t&&(n=n.toLowerCase()),r[n]=!0;return r}function tl(e){if(!e)return null;let t=ri(e.tags,!0),r=ri(e.ids),n=ri(e.classes);return t===null&&r===null&&n===null?null:{tags:t,ids:r,classes:n}}function rl(e){let t=!1;if(e.scopes&&Array.isArray(e.scopes)){t=Object.create(null);for(let r=0;r<e.scopes.length;r++){let n=e.scopes[r];if(!n||!Array.isArray(n))throw new Error("Wrong usage format");for(let o of n){if(Sm.call(t,o))throw new Error(`Class can't be used for several scopes: ${o}`);t[o]=r+1}}}return{whitelist:tl(e),blacklist:tl(e.blacklist),scopes:t}}function Ce(e){return!e||!e.children||e.children.isEmpty}function ni(e,t){return e!==null&&e.children===t}function oi(e,t,r){if(e.block&&(this.stylesheet!==null&&(this.stylesheet.firstAtrulesAllowed=!1),Ce(e.block))){r.remove(t);return}switch(e.name){case"charset":if(Ce(e.prelude)){r.remove(t);return}if(t.prev){r.remove(t);return}break;case"import":if(this.stylesheet===null||!this.stylesheet.firstAtrulesAllowed){r.remove(t);return}r.prevUntil(t.prev,function(n){if(!(n.type==="Atrule"&&(n.name==="import"||n.name==="charset")))return this.root.firstAtrulesAllowed=!1,r.remove(t),!0},this);break;default:{let n=Z(e.name).basename;(n==="keyframes"||n==="media"||n==="supports")&&(Ce(e.prelude)||Ce(e.block))&&r.remove(t)}}}function ii(e,t,r){r.remove(t)}function ai(e,t,r){if(e.value.children&&e.value.children.isEmpty){r.remove(t);return}me(e.property).custom&&/\S/.test(e.value.value)&&(e.value.value=e.value.value.trim())}function si(e,t,r){(ni(this.stylesheet,r)||ni(this.block,r))&&r.remove(t)}var{hasOwnProperty:ot}=Object.prototype,Cm=new Set(["keyframes"]);function nl(e,t){return e.children.forEach((r,n,o)=>{let i=!1;F(r,function(a){if(this.selector===null||this.selector===e)switch(a.type){case"SelectorList":(this.function===null||this.function.name.toLowerCase()!=="not")&&nl(a,t)&&(i=!0);break;case"ClassSelector":t.whitelist!==null&&t.whitelist.classes!==null&&!ot.call(t.whitelist.classes,a.name)&&(i=!0),t.blacklist!==null&&t.blacklist.classes!==null&&ot.call(t.blacklist.classes,a.name)&&(i=!0);break;case"IdSelector":t.whitelist!==null&&t.whitelist.ids!==null&&!ot.call(t.whitelist.ids,a.name)&&(i=!0),t.blacklist!==null&&t.blacklist.ids!==null&&ot.call(t.blacklist.ids,a.name)&&(i=!0);break;case"TypeSelector":a.name.charAt(a.name.length-1)!=="*"&&(t.whitelist!==null&&t.whitelist.tags!==null&&!ot.call(t.whitelist.tags,a.name.toLowerCase())&&(i=!0),t.blacklist!==null&&t.blacklist.tags!==null&&ot.call(t.blacklist.tags,a.name.toLowerCase())&&(i=!0));break}}),i&&o.remove(n)}),e.children.isEmpty}function li(e,t,r,n){if(Ce(e.prelude)||Ce(e.block)){r.remove(t);return}if(this.atrule&&Cm.has(Z(this.atrule.name).basename))return;let{usage:o}=n;if(o&&(o.whitelist!==null||o.blacklist!==null)&&(nl(e.prelude,o),Ce(e.prelude))){r.remove(t);return}}function ci(e,t,r){if(t.data.name!=="*")return;let o=t.next&&t.next.data.type;(o==="IdSelector"||o==="ClassSelector"||o==="AttributeSelector"||o==="PseudoClassSelector"||o==="PseudoElementSelector")&&r.remove(t)}function ui(e,t,r){r.remove(t)}var ol={Atrule:oi,Comment:ii,Declaration:ai,Raw:si,Rule:li,TypeSelector:ci,WhiteSpace:ui};function il(e,t){F(e,{leave(r,n,o){ol.hasOwnProperty(r.type)&&ol[r.type].call(this,r,n,o,t)}})}function al(e){e.block.children.forEach(t=>{t.prelude.children.forEach(r=>{r.children.forEach((n,o)=>{n.type==="Percentage"&&n.value==="100"?o.data={type:"TypeSelector",loc:n.loc,name:"to"}:n.type==="TypeSelector"&&n.name==="from"&&(o.data={type:"Percentage",loc:n.loc,value:"0"})})})})}function sl(e){Z(e.name).basename==="keyframes"&&al(e)}var Am=/^(-?\d|--)|[\u0000-\u002c\u002e\u002f\u003A-\u0040\u005B-\u005E\u0060\u007B-\u009f]/;function Em(e){return e===""||e==="-"?!1:!Am.test(e)}function ll(e){let t=e.value;!t||t.type!=="String"||Em(t.value)&&(e.value={type:"Identifier",loc:t.loc,name:t.value})}function pi(e){let t=e.children;t.forEachRight(function(r,n){if(r.type==="Identifier"){if(r.name==="bold")n.data={type:"Number",loc:r.loc,value:"700"};else if(r.name==="normal"){let o=n.prev;o&&o.data.type==="Operator"&&o.data.value==="/"&&this.remove(o),this.remove(n)}}}),t.isEmpty&&t.insert(t.createItem({type:"Identifier",name:"normal"}))}function hi(e){let t=e.children.head.data;if(t.type==="Identifier")switch(t.name){case"normal":e.children.head.data={type:"Number",loc:t.loc,value:"400"};break;case"bold":e.children.head.data={type:"Number",loc:t.loc,value:"700"};break}}function fi(e){function t(){n.length||n.unshift({type:"Number",loc:null,value:"0"},{type:"Number",loc:null,value:"0"}),r.push.apply(r,n),n=[]}let r=[],n=[];e.children.forEach(o=>{if(o.type==="Operator"&&o.value===","){t(),r.push(o);return}o.type==="Identifier"&&(o.name==="transparent"||o.name==="none"||o.name==="repeat"||o.name==="scroll")||n.push(o)}),t(),e.children=new w().fromArray(r)}function Nt(e){e.children.forEach((t,r,n)=>{t.type==="Identifier"&&t.name.toLowerCase()==="none"&&(n.head===n.tail?r.data={type:"Number",loc:t.loc,value:"0"}:n.remove(r))})}var cl={font:pi,"font-weight":hi,background:fi,border:Nt,outline:Nt};function mi(e){if(!this.declaration)return;let t=me(this.declaration.property);cl.hasOwnProperty(t.basename)&&cl[t.basename](e)}var Tm=/^(?:\+|(-))?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,Lm=/^([\+\-])?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,Pm=new Set(["Dimension","Hash","Identifier","Number","Raw","UnicodeRange"]);function _e(e,t){let r=t&&t.prev!==null&&Pm.has(t.prev.data.type)?Lm:Tm;return e=String(e).replace(r,"$1$2$3"),(e===""||e==="-")&&(e="0"),e}function ul(e){e.value=_e(e.value)}var Im=new Set(["calc","min","max","clamp"]),Om=new Set(["px","mm","cm","in","pt","pc","em","ex","ch","rem","vh","vw","vmin","vmax","vm"]);function di(e,t){let r=_e(e.value);if(e.value=r,r==="0"&&this.declaration!==null&&this.atrulePrelude===null){let n=e.unit.toLowerCase();if(!Om.has(n)||this.declaration.property==="-ms-flex"||this.declaration.property==="flex"||this.function&&Im.has(this.function.name))return;t.data={type:"Number",loc:e.loc,value:r}}}var Dm=new Set(["width","min-width","max-width","height","min-height","max-height","flex","-ms-flex"]);function gi(e,t){e.value=_e(e.value),e.value==="0"&&this.declaration&&!Dm.has(this.declaration.property)&&(t.data={type:"Number",loc:e.loc,value:e.value},xr.matchDeclaration(this.declaration).isType(t.data,"length")||(t.data=e))}function pl(e){e.value=e.value.replace(/\\/g,"/")}var hl={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},fl={8e5:"maroon",800080:"purple",808e3:"olive",808080:"gray","00ffff":"cyan",f0ffff:"azure",f5f5dc:"beige",ffe4c4:"bisque","000000":"black","0000ff":"blue",a52a2a:"brown",ff7f50:"coral",ffd700:"gold","008000":"green","4b0082":"indigo",fffff0:"ivory",f0e68c:"khaki","00ff00":"lime",faf0e6:"linen","000080":"navy",ffa500:"orange",da70d6:"orchid",cd853f:"peru",ffc0cb:"pink",dda0dd:"plum",f00:"red",ff0000:"red",fa8072:"salmon",a0522d:"sienna",c0c0c0:"silver",fffafa:"snow",d2b48c:"tan","008080":"teal",ff6347:"tomato",ee82ee:"violet",f5deb3:"wheat",ffffff:"white",ffff00:"yellow"};function bi(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function ml(e,t,r,n){let o,i,a;if(t===0)o=i=a=r;else{let l=r<.5?r*(1+t):r+t-r*t,c=2*r-l;o=bi(c,l,e+1/3),i=bi(c,l,e),a=bi(c,l,e-1/3)}return[Math.round(o*255),Math.round(i*255),Math.round(a*255),n]}function yi(e){return e=e.toString(16),e.length===1?"0"+e:e}function xi(e,t,r){let n=e.head,o=[],i=!1;for(;n!==null;){let{type:a,value:l}=n.data;switch(a){case"Number":case"Percentage":if(i)return;i=!0,o.push({type:a,value:Number(l)});break;case"Operator":if(l===","){if(!i)return;i=!1}else if(i||l!=="+")return;break;default:return}n=n.next}if(o.length===t){if(o.length===4){if(o[3].type!=="Number")return;o[3].type="Alpha"}if(r){if(o[0].type!==o[1].type||o[0].type!==o[2].type)return}else{if(o[0].type!=="Number"||o[1].type!=="Percentage"||o[2].type!=="Percentage")return;o[0].type="Angle"}return o.map(function(a){let l=Math.max(0,a.value);switch(a.type){case"Number":l=Math.min(l,255);break;case"Percentage":if(l=Math.min(l,100)/100,!r)return l;l=255*l;break;case"Angle":return(l%360+360)%360/360;case"Alpha":return Math.min(l,1)}return Math.round(l)})}}function dl(e,t){let r=e.name,n;if(r==="rgba"||r==="hsla"){if(n=xi(e.children,4,r==="rgba"),!n)return;if(r==="hsla"&&(n=ml(...n),e.name="rgba"),n[3]===0){let o=this.function&&this.function.name;if(n[0]===0&&n[1]===0&&n[2]===0||!/^(?:to|from|color-stop)$|gradient$/i.test(o)){t.data={type:"Identifier",loc:e.loc,name:"transparent"};return}}if(n[3]!==1){e.children.forEach((o,i,a)=>{if(o.type==="Operator"){o.value!==","&&a.remove(i);return}i.data={type:"Number",loc:o.loc,value:_e(n.shift())}});return}r="rgb"}if(r==="hsl"){if(n=n||xi(e.children,3,!1),!n)return;n=ml(...n),r="rgb"}if(r==="rgb"){if(n=n||xi(e.children,3,!0),!n)return;t.data={type:"Hash",loc:e.loc,value:yi(n[0])+yi(n[1])+yi(n[2])},ki(t.data,t)}}function gl(e,t){if(this.declaration===null)return;let r=e.name.toLowerCase();if(hl.hasOwnProperty(r)&&xr.matchDeclaration(this.declaration).isType(e,"color")){let n=hl[r];n.length+1<=r.length?t.data={type:"Hash",loc:e.loc,value:n}:(r==="grey"&&(r="gray"),e.name=r)}}function ki(e,t){let r=e.value.toLowerCase();r.length===6&&r[0]===r[1]&&r[2]===r[3]&&r[4]===r[5]&&(r=r[0]+r[2]+r[4]),fl[r]?t.data={type:"Identifier",loc:e.loc,name:fl[r]}:e.value=r}var bl={Atrule:sl,AttributeSelector:ll,Value:mi,Dimension:di,Percentage:gi,Number:ul,Url:pl,Hash:ki,Identifier:gl,Function:dl};function yl(e){F(e,{leave(t,r,n){bl.hasOwnProperty(t.type)&&bl[t.type].call(this,t,r,n)}})}var wi=class{constructor(){this.map=new Map}resolve(t){let r=this.map.get(t);return r===void 0&&(r=this.map.size+1,this.map.set(t,r)),r}};function vi(){let e=new wi;return function(r){let n=ne(r);return r.id=e.resolve(n),r.length=n.length,r.fingerprint=null,r}}function Nm(e){return e.type==="Raw"?Js(e.value,{context:"selectorList"}):e}function Mm(e,t){for(let r=0;r<3;r++)if(e[r]!==t[r])return e[r]>t[r]?e:t;return e}function xl(e){return Nm(e).children.reduce((t,r)=>Mm(kl(r),t),[0,0,0])}function kl(e){let t=0,r=0,n=0;return e.children.forEach(o=>{switch(o.type){case"IdSelector":t++;break;case"ClassSelector":case"AttributeSelector":r++;break;case"PseudoClassSelector":switch(o.name.toLowerCase()){case"not":case"has":case"is":case"matches":case"-webkit-any":case"-moz-any":{let[i,a,l]=xl(o.children.first);t+=i,r+=a,n+=l;break}case"nth-child":case"nth-last-child":{let i=o.children.first;if(i.type==="Nth"&&i.selector){let[a,l,c]=xl(i.selector);t+=a,r+=l+1,n+=c}else r++;break}case"where":break;case"before":case"after":case"first-line":case"first-letter":n++;break;default:r++}break;case"TypeSelector":o.name.endsWith("*")||n++;break;case"PseudoElementSelector":n++;break}}),[t,r,n]}var kr=kl;var Rm=new Set(["first-letter","first-line","after","before"]),zm=new Set(["link","visited","hover","active","first-letter","first-line","after","before"]);function Mt(e,t){let r=new Set;e.prelude.children.forEach(function(n){let o="*",i=0;n.children.forEach(function(a){switch(a.type){case"ClassSelector":if(t&&t.scopes){let l=t.scopes[a.name]||0;if(i!==0&&l!==i)throw new Error("Selector can't has classes from different scopes: "+ne(n));i=l}break;case"PseudoClassSelector":{let l=a.name.toLowerCase();zm.has(l)||r.add(`:${l}`);break}case"PseudoElementSelector":{let l=a.name.toLowerCase();Rm.has(l)||r.add(`::${l}`);break}case"TypeSelector":o=a.name.toLowerCase();break;case"AttributeSelector":a.flags&&r.add(`[${a.flags.toLowerCase()}]`);break;case"Combinator":o="*";break}}),n.compareMarker=kr(n).toString(),n.id=null,n.id=ne(n),i&&(n.compareMarker+=":"+i),o!=="*"&&(n.compareMarker+=","+o)}),e.pseudoSignature=r.size>0?[...r].sort().join(","):!1}function Si(e,t){let r=vi();return F(e,{visit:"Rule",enter(n){n.block.children.forEach(r),Mt(n,t.usage)}}),F(e,{visit:"Atrule",enter(n){n.prelude&&(n.prelude.id=null,n.prelude.id=ne(n.prelude)),Z(n.name).basename==="keyframes"&&(n.block.avoidRulesMerge=!0,n.block.children.forEach(function(o){o.prelude.children.forEach(function(i){i.compareMarker=i.id})}))}}),{declaration:r}}var{hasOwnProperty:wl}=Object.prototype;function vl(e,t,r,n){let o=t.data,i=Z(o.name).basename,a=o.name.toLowerCase()+"/"+(o.prelude?o.prelude.id:null);wl.call(e,i)||(e[i]=Object.create(null)),n&&delete e[i][a],wl.call(e[i],a)||(e[i][a]=new w),e[i][a].append(r.remove(t))}function Fm(e,t){let r=Object.create(null),n=null;e.children.forEach(function(o,i,a){if(o.type==="Atrule"){let l=Z(o.name).basename;switch(l){case"keyframes":vl(r,i,a,!0);return;case"media":if(t.forceMediaMerge){vl(r,i,a,!1);return}break}n===null&&l!=="charset"&&l!=="import"&&(n=i)}else n===null&&(n=i)});for(let o in r)for(let i in r[o])e.children.insertList(r[o][i],o==="media"?null:n)}function Sl(e){return e.type==="Atrule"&&e.name==="media"}function Bm(e,t,r){if(!Sl(e))return;let n=t.prev&&t.prev.data;!n||!Sl(n)||e.prelude&&n.prelude&&e.prelude.id===n.prelude.id&&(n.block.children.appendList(e.block.children),r.remove(t))}function Ci(e,t){Fm(e,t),F(e,{visit:"Atrule",reverse:!0,enter:Bm})}var{hasOwnProperty:_m}=Object.prototype;function Rt(e,t){let r=e.head,n=t.head;for(;r!==null&&n!==null&&r.data.id===n.data.id;)r=r.next,n=n.next;return r===null&&n===null}function zt(e,t){let r=e.head,n=t.head;for(;r!==null&&n!==null&&r.data.id===n.data.id;)r=r.next,n=n.next;return r===null&&n===null}function Ai(e,t){let r={eq:[],ne1:[],ne2:[],ne2overrided:[]},n=Object.create(null),o=Object.create(null);for(let i=t.head;i;i=i.next)o[i.data.id]=!0;for(let i=e.head;i;i=i.next){let a=i.data;a.fingerprint&&(n[a.fingerprint]=a.important),o[a.id]?(o[a.id]=!1,r.eq.push(a)):r.ne1.push(a)}for(let i=t.head;i;i=i.next){let a=i.data;o[a.id]&&((!_m.call(n,a.fingerprint)||!n[a.fingerprint]&&a.important)&&r.ne2.push(a),r.ne2overrided.push(a))}return r}function Oe(e,t){return t.forEach(r=>{let n=r.id,o=e.head;for(;o;){let i=o.data.id;if(i===n)return;if(i>n)break;o=o.next}e.insert(e.createItem(r),o)}),e}function wr(e,t){let r=e.head;for(;r!==null;){let n=t.head;for(;n!==null;){if(r.data.compareMarker===n.data.compareMarker)return!0;n=n.next}r=r.next}return!1}function De(e){switch(e.type){case"Rule":return wr(e.prelude.children,this);case"Atrule":if(e.block)return e.block.children.some(De,this);break;case"Declaration":return!1}return!0}function Um(e,t,r){let n=e.prelude.children,o=e.block.children;r.prevUntil(t.prev,function(i){if(i.type!=="Rule")return De.call(n,i);let a=i.prelude.children,l=i.block.children;if(e.pseudoSignature===i.pseudoSignature){if(Rt(a,n))return l.appendList(o),r.remove(t),!0;if(zt(o,l))return Oe(a,n),r.remove(t),!0}return wr(n,a)})}function Ei(e){F(e,{visit:"Rule",enter:Um})}function jm(e,t,r){let n=e.prelude.children;for(;n.head!==n.tail;){let o=new w;o.insert(n.remove(n.head)),r.insert(r.createItem({type:"Rule",loc:e.loc,prelude:{type:"SelectorList",loc:e.prelude.loc,children:o},block:{type:"Block",loc:e.block.loc,children:e.block.children.copy()},pseudoSignature:e.pseudoSignature}),t)}}function Ti(e){F(e,{visit:"Rule",reverse:!0,enter:jm})}var El=1,qm=2,it=0,Ft=1,Li=2,vr=3,Cl=["top","right","bottom","left"],Hm={"margin-top":"top","margin-right":"right","margin-bottom":"bottom","margin-left":"left","padding-top":"top","padding-right":"right","padding-bottom":"bottom","padding-left":"left","border-top-color":"top","border-right-color":"right","border-bottom-color":"bottom","border-left-color":"left","border-top-width":"top","border-right-width":"right","border-bottom-width":"bottom","border-left-width":"left","border-top-style":"top","border-right-style":"right","border-bottom-style":"bottom","border-left-style":"left"},Al={margin:"margin","margin-top":"margin","margin-right":"margin","margin-bottom":"margin","margin-left":"margin",padding:"padding","padding-top":"padding","padding-right":"padding","padding-bottom":"padding","padding-left":"padding","border-color":"border-color","border-top-color":"border-color","border-right-color":"border-color","border-bottom-color":"border-color","border-left-color":"border-color","border-width":"border-width","border-top-width":"border-width","border-right-width":"border-width","border-bottom-width":"border-width","border-left-width":"border-width","border-style":"border-style","border-top-style":"border-style","border-right-style":"border-style","border-bottom-style":"border-style","border-left-style":"border-style"},Pi=class{constructor(t){this.name=t,this.loc=null,this.iehack=void 0,this.sides={top:null,right:null,bottom:null,left:null}}getValueSequence(t,r){let n=[],o="";return t.value.type!=="Value"||t.value.children.some(function(a){let l=!1;switch(a.type){case"Identifier":switch(a.name){case"\\0":case"\\9":o=a.name;return;case"inherit":case"initial":case"unset":case"revert":l=a.name;break}break;case"Dimension":switch(a.unit){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":l=a.unit;break}break;case"Hash":case"Number":case"Percentage":break;case"Function":if(a.name==="var")return!0;l=a.name;break;default:return!0}n.push({node:a,special:l,important:t.important})})||n.length>r||typeof this.iehack=="string"&&this.iehack!==o?!1:(this.iehack=o,n)}canOverride(t,r){let n=this.sides[t];return!n||r.important&&!n.important}add(t,r){function n(){let o=this.sides,i=Hm[t];if(i){if(!(i in o))return!1;let a=this.getValueSequence(r,1);if(!a||!a.length)return!1;for(let l in o)if(o[l]!==null&&o[l].special!==a[0].special)return!1;return this.canOverride(i,a[0])&&(o[i]=a[0]),!0}else if(t===this.name){let a=this.getValueSequence(r,4);if(!a||!a.length)return!1;switch(a.length){case 1:a[Ft]=a[it],a[Li]=a[it],a[vr]=a[it];break;case 2:a[Li]=a[it],a[vr]=a[Ft];break;case 3:a[vr]=a[Ft];break}for(let l=0;l<4;l++)for(let c in o)if(o[c]!==null&&o[c].special!==a[l].special)return!1;for(let l=0;l<4;l++)this.canOverride(Cl[l],a[l])&&(o[Cl[l]]=a[l]);return!0}}return n.call(this)?(this.loc||(this.loc=r.loc),!0):!1}isOkToMinimize(){let t=this.sides.top,r=this.sides.right,n=this.sides.bottom,o=this.sides.left;if(t&&r&&n&&o){let i=t.important+r.important+n.important+o.important;return i===0||i===4}return!1}getValue(){let t=new w,r=this.sides,n=[r.top,r.right,r.bottom,r.left],o=[ne(r.top.node),ne(r.right.node),ne(r.bottom.node),ne(r.left.node)];o[vr]===o[Ft]&&(n.pop(),o[Li]===o[it]&&(n.pop(),o[Ft]===o[it]&&n.pop()));for(let i=0;i<n.length;i++)t.appendData(n[i].node);return this.iehack&&t.appendData({type:"Identifier",loc:null,name:this.iehack}),{type:"Value",loc:null,children:t}}getDeclaration(){return{type:"Declaration",loc:this.loc,important:this.sides.top.important,property:this.name,value:this.getValue()}}};function Wm(e,t,r,n){let o=e.block.children,i=e.prelude.children.first.id;return e.block.children.forEachRight(function(a,l){let c=a.property;if(!Al.hasOwnProperty(c))return;let s=Al[c],u,p;if((!n||i===n)&&s in t&&(p=qm,u=t[s]),(!u||!u.add(c,a))&&(p=El,u=new Pi(s),!u.add(c,a))){n=null;return}t[s]=u,r.push({operation:p,block:o,item:l,shorthand:u}),n=i}),n}function Ym(e,t){e.forEach(function(r){let n=r.shorthand;!n.isOkToMinimize()||(r.operation===El?r.item.data=t(n.getDeclaration()):r.block.remove(r.item))})}function Ii(e,t){let r={},n=[];F(e,{visit:"Rule",reverse:!0,enter(o){let i=this.block||this.stylesheet,a=(o.pseudoSignature||"")+"|"+o.prelude.children.first.id,l,c;r.hasOwnProperty(i.id)?l=r[i.id]:(l={lastShortSelector:null},r[i.id]=l),l.hasOwnProperty(a)?c=l[a]:(c={},l[a]=c),l.lastShortSelector=Wm.call(this,o,c,n,l.lastShortSelector)}}),Ym(n,t.declaration)}var Vm=1,Gm=new Set(["src"]),Tl={display:/table|ruby|flex|-(flex)?box$|grid|contents|run-in/i,"text-align":/^(start|end|match-parent|justify-all)$/i},Ll={cursor:["auto","crosshair","default","move","text","wait","help","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","pointer","progress","not-allowed","no-drop","vertical-text","all-scroll","col-resize","row-resize"],overflow:["hidden","visible","scroll","auto"],position:["static","relative","absolute","fixed"]},Pl={"border-width":["border"],"border-style":["border"],"border-color":["border"],"border-top":["border"],"border-right":["border"],"border-bottom":["border"],"border-left":["border"],"border-top-width":["border-top","border-width","border"],"border-right-width":["border-right","border-width","border"],"border-bottom-width":["border-bottom","border-width","border"],"border-left-width":["border-left","border-width","border"],"border-top-style":["border-top","border-style","border"],"border-right-style":["border-right","border-style","border"],"border-bottom-style":["border-bottom","border-style","border"],"border-left-style":["border-left","border-style","border"],"border-top-color":["border-top","border-color","border"],"border-right-color":["border-right","border-color","border"],"border-bottom-color":["border-bottom","border-color","border"],"border-left-color":["border-left","border-color","border"],"margin-top":["margin"],"margin-right":["margin"],"margin-bottom":["margin"],"margin-left":["margin"],"padding-top":["padding"],"padding-right":["padding"],"padding-bottom":["padding"],"padding-left":["padding"],"font-style":["font"],"font-variant":["font"],"font-weight":["font"],"font-size":["font"],"font-family":["font"],"list-style-type":["list-style"],"list-style-position":["list-style"],"list-style-image":["list-style"]};function Il(e,t,r){let n=me(e).basename;if(n==="background")return e+":"+ne(t.value);let o=t.id,i=r[o];if(!i){switch(t.value.type){case"Value":let a={},l="",c="",s=!1;t.value.children.forEach(function u(p){switch(p.type){case"Value":case"Brackets":case"Parentheses":p.children.forEach(u);break;case"Raw":s=!0;break;case"Identifier":{let{name:h}=p;l||(l=Z(h).vendor),/\\[09]/.test(h)&&(c=RegExp.lastMatch),Ll.hasOwnProperty(n)?Ll[n].indexOf(h)===-1&&(a[h]=!0):Tl.hasOwnProperty(n)&&Tl[n].test(h)&&(a[h]=!0);break}case"Function":{let{name:h}=p;l||(l=Z(h).vendor),h==="rect"&&(p.children.some(b=>b.type==="Operator"&&b.value===",")||(h="rect-backward")),a[h+"()"]=!0,p.children.forEach(u);break}case"Dimension":{let{unit:h}=p;switch(/\\[09]/.test(h)&&(c=RegExp.lastMatch),h){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":a[h]=!0;break}break}}}),i=s?"!"+Vm++:"!"+Object.keys(a).sort()+"|"+c+l;break;case"Raw":i="!"+t.value.value;break;default:i=ne(t.value)}r[o]=i}return e+i}function Km(e,t,r){let n=me(t.property);if(Pl.hasOwnProperty(n.basename)){let o=Pl[n.basename];for(let i of o){let a=Il(n.prefix+i,t,r),l=e.hasOwnProperty(a)?e[a]:null;if(l&&(!t.important||l.item.data.important))return l}}}function Qm(e,t,r,n,o){let i=e.block.children;i.forEachRight(function(a,l){let{property:c}=a,s=Il(c,a,o),u=n[s];u&&!Gm.has(c)?a.important&&!u.item.data.important?(n[s]={block:i,item:l},u.block.remove(u.item)):i.remove(l):Km(n,a,o)?i.remove(l):(a.fingerprint=s,n[s]={block:i,item:l})}),i.isEmpty&&r.remove(t)}function Oi(e){let t={},r=Object.create(null);F(e,{visit:"Rule",reverse:!0,enter(n,o,i){let a=this.block||this.stylesheet,l=(n.pseudoSignature||"")+"|"+n.prelude.children.first.id,c,s;t.hasOwnProperty(a.id)?c=t[a.id]:(c={},t[a.id]=c),c.hasOwnProperty(l)?s=c[l]:(s={},c[l]=s),Qm.call(this,n,o,i,s,r)}})}function $m(e,t,r){let n=e.prelude.children,o=e.block.children,i=n.first.compareMarker,a={};r.nextUntil(t.next,function(l,c){if(l.type!=="Rule")return De.call(n,l);if(e.pseudoSignature!==l.pseudoSignature)return!0;let s=l.prelude.children.head,u=l.block.children,p=s.data.compareMarker;if(p in a)return!0;if(n.head===n.tail&&n.first.id===s.data.id){o.appendList(u),r.remove(c);return}if(zt(o,u)){let h=s.data.id;n.some((m,b)=>{let T=m.id;if(h<T)return n.insert(s,b),!0;if(!b.next)return n.insert(s),!0}),r.remove(c);return}if(p===i)return!0;a[p]=!0})}function Di(e){F(e,{visit:"Rule",enter:$m})}function Ni(e){return e.reduce((t,r)=>t+r.id.length+1,0)-1}function Mi(e){let t=0;for(let r of e)t+=r.length;return t+e.length-1}function Xm(e,t,r){let n=this.block!==null?this.block.avoidRulesMerge:!1,o=e.prelude.children,i=e.block,a=Object.create(null),l=!0,c=!0;r.prevUntil(t.prev,function(s,u){let p=s.block,h=s.type;if(h!=="Rule"){let T=De.call(o,s);return!T&&h==="Atrule"&&p&&F(p,{visit:"Rule",enter(N){N.prelude.children.forEach(C=>{a[C.compareMarker]=!0})}}),T}if(e.pseudoSignature!==s.pseudoSignature)return!0;let m=s.prelude.children;if(c=!m.some(T=>T.compareMarker in a),!c&&!l)return!0;if(l&&Rt(m,o))return p.children.appendList(i.children),r.remove(t),!0;let b=Ai(i.children,p.children);if(b.eq.length){if(!b.ne1.length&&!b.ne2.length)return c&&(Oe(o,m),r.remove(u)),!0;if(!n)if(b.ne1.length&&!b.ne2.length){let T=Ni(o),N=Mi(b.eq);l&&T<N&&(Oe(m,o),i.children.fromArray(b.ne1))}else if(!b.ne1.length&&b.ne2.length){let T=Ni(m),N=Mi(b.eq);c&&T<N&&(Oe(o,m),p.children.fromArray(b.ne2))}else{let T={type:"SelectorList",loc:null,children:Oe(m.copy(),o)},N=Ni(T.children)+2;if(Mi(b.eq)>=N){let D=r.createItem({type:"Rule",loc:null,prelude:T,block:{type:"Block",loc:null,children:new w().fromArray(b.eq)},pseudoSignature:e.pseudoSignature});return i.children.fromArray(b.ne1),p.children.fromArray(b.ne2overrided),l?r.insert(D,u):r.insert(D,t),!0}}}l&&(l=!m.some(T=>o.some(N=>N.compareMarker===T.compareMarker))),m.forEach(T=>{a[T.compareMarker]=!0})})}function Ri(e){F(e,{visit:"Rule",reverse:!0,enter:Xm})}function Ol(e,t){let r=Si(e,t);t.logger("prepare",e),Ci(e,t),t.logger("mergeAtrule",e),Ei(e),t.logger("initialMergeRuleset",e),Ti(e),t.logger("disjoinRuleset",e),Ii(e,r),t.logger("restructShorthand",e),Oi(e),t.logger("restructBlock",e),Di(e),t.logger("mergeRuleset",e),Ri(e),t.logger("restructRuleset",e)}function Zm(e,t){let r=new w,n=!1,o;return e.nextUntil(e.head,(i,a,l)=>{if(i.type==="Comment"){if(!t||i.value.charAt(0)!=="!"){l.remove(a);return}if(n||o)return!0;l.remove(a),o=i;return}i.type!=="WhiteSpace"&&(n=!0),r.insert(l.remove(a))}),{comment:o,stylesheet:{type:"StyleSheet",loc:null,children:r}}}function Jm(e,t,r,n){n.logger(`Compress block #${r}`,null,!0);let o=1;return e.type==="StyleSheet"&&(e.firstAtrulesAllowed=t,e.id=o++),F(e,{visit:"Atrule",enter(i){i.block!==null&&(i.block.id=o++)}}),n.logger("init",e),il(e,n),n.logger("clean",e),yl(e,n),n.logger("replace",e),n.restructuring&&Ol(e,n),e}function ed(e){let t="comments"in e?e.comments:"exclamation";return typeof t=="boolean"?t=t?"exclamation":!1:t!=="exclamation"&&t!=="first-exclamation"&&(t=!1),t}function td(e){return"restructure"in e?e.restructure:"restructuring"in e?e.restructuring:!0}function rd(e){return new w().appendData({type:"Rule",loc:null,prelude:{type:"SelectorList",loc:null,children:new w().appendData({type:"Selector",loc:null,children:new w().appendData({type:"TypeSelector",loc:null,name:"x"})})},block:e})}function zi(e,t){e=e||{type:"StyleSheet",loc:null,children:new w},t=t||{};let r={logger:typeof t.logger=="function"?t.logger:function(){},restructuring:td(t),forceMediaMerge:Boolean(t.forceMediaMerge),usage:t.usage?rl(t.usage):!1},n=new w,o=ed(t),i=!0,a,l,c=1,s;t.clone&&(e=Dt(e)),e.type==="StyleSheet"?(a=e.children,e.children=n):a=rd(e);do{if(l=Zm(a,Boolean(o)),Jm(l.stylesheet,i,c++,r),s=l.stylesheet.children,l.comment&&(n.isEmpty||n.insert(w.createItem({type:"Raw",value:`
`})),n.insert(w.createItem(l.comment)),s.isEmpty||n.insert(w.createItem({type:"Raw",value:`
`}))),i&&!s.isEmpty){let u=s.last;(u.type!=="Atrule"||u.name!=="import"&&u.name!=="charset")&&(i=!1)}o!=="exclamation"&&(o=!1),n.appendList(s)}while(!a.isEmpty);return{ast:e}}function Dl(e){let t=nt.encode(e,!0),r=nt.encode(e);return t.length<r.length?t:r}var{lexer:nd,tokenize:od,parse:id,generate:ad,walk:sd,find:ld,findLast:cd,findAll:ud,fromPlainObject:pd,toPlainObject:hd}=el({node:{String:{generate(e){this.token(Ee.String,Dl(e.value))}},Url:{generate(e){let t=Pt.encode(e.value),r=Dl(e.value);this.token(Ee.Url,t.length<=r.length+5?t:"url("+r+")")}}}});var Nl={};y(Nl,{addSelectors:()=>Oe,compareDeclarations:()=>Ai,hasSimilarSelectors:()=>wr,isEqualDeclarations:()=>zt,isEqualSelectors:()=>Rt,processSelector:()=>Mt,unsafeToSkipNode:()=>De});var{parse:fd,generate:Bi,compress:md}=Fi;function at(e,t,r,n){return t.debug&&console.error(`## ${e} done in %d ms
`,Date.now()-r),n}function dd(e){let t;return function(n,o){let i=n;if(o&&(i=`[${((Date.now()-t)/1e3).toFixed(3)}s] ${i}`),e>1&&o){let a=Bi(o);e===2&&a.length>256&&(a=a.substr(0,256)+"..."),i+=`
  ${a}
`}console.error(i),t=Date.now()}}function gd(e){return e={...e},typeof e.logger!="function"&&e.debug&&(e.logger=dd(e.debug)),e}function Ml(e,t,r){Array.isArray(r)||(r=[r]),r.forEach(n=>n(e,t))}function Rl(e,t,r){r=r||{};let n=r.filename||"<unknown>",o,i=at("parsing",r,Date.now(),fd(t,{context:e,filename:n,positions:Boolean(r.sourceMap)}));r.beforeCompress&&at("beforeCompress",r,Date.now(),Ml(i,r,r.beforeCompress));let a=at("compress",r,Date.now(),md(i,gd(r)));return r.afterCompress&&at("afterCompress",r,Date.now(),Ml(a,r,r.afterCompress)),r.sourceMap?o=at("generate(sourceMap: true)",r,Date.now(),(()=>{let l=Bi(a.ast,{sourceMap:!0});return l.map._file=n,l.map.setSourceContent(n,t),l})()):o=at("generate",r,Date.now(),{css:Bi(a.ast),map:null}),o}function X0(e,t){return Rl("stylesheet",e,t)}function Z0(e,t){return Rl("declarationList",e,t)}export{X0 as minify,Z0 as minifyBlock,Fi as syntax,Nl as utils,Hl as version};

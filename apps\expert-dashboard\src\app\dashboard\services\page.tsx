'use client';

import { useState } from 'react';
import { 
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  ClockIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

interface Service {
  id: string;
  title: string;
  description: string;
  category: string;
  price: number;
  deliveryTime: number;
  status: 'active' | 'pending' | 'rejected' | 'draft';
  featured: boolean;
  rating: number;
  reviewCount: number;
  orderCount: number;
  views: number;
  createdAt: string;
  images: string[];
}

const mockServices: Service[] = [
  {
    id: '1',
    title: 'تصميم مواقع الويب الاحترافية',
    description: 'تصميم وتطوير مواقع ويب حديثة ومتجاوبة باستخدام أحدث التقنيات مع ضمان الجودة والسرعة',
    category: 'تطوير الويب',
    price: 500,
    deliveryTime: 7,
    status: 'active',
    featured: true,
    rating: 4.8,
    reviewCount: 24,
    orderCount: 47,
    views: 1250,
    createdAt: '2024-01-15',
    images: [],
  },
  {
    id: '2',
    title: 'تطوير تطبيقات الموبايل',
    description: 'تطوير تطبيقات iOS و Android باستخدام React Native مع واجهات مستخدم جذابة',
    category: 'تطوير التطبيقات',
    price: 800,
    deliveryTime: 14,
    status: 'pending',
    featured: false,
    rating: 4.9,
    reviewCount: 18,
    orderCount: 32,
    views: 890,
    createdAt: '2024-01-20',
    images: [],
  },
  {
    id: '3',
    title: 'استشارات تقنية متخصصة',
    description: 'استشارات في مجال التكنولوجيا والحلول الرقمية لمساعدتك في اتخاذ القرارات الصحيحة',
    category: 'استشارات',
    price: 100,
    deliveryTime: 3,
    status: 'draft',
    featured: false,
    rating: 4.5,
    reviewCount: 12,
    orderCount: 25,
    views: 456,
    createdAt: '2024-01-18',
    images: [],
  },
];

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>(mockServices);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  const filteredServices = services.filter(service => {
    return selectedStatus === 'all' || service.status === selectedStatus;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      draft: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    };
    
    const labels = {
      active: 'نشط',
      pending: 'في الانتظار',
      rejected: 'مرفوض',
      draft: 'مسودة',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  const handleDelete = (serviceId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
      setServices(services.filter(service => service.id !== serviceId));
    }
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            خدماتي
          </h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            إدارة جميع خدماتك المنشورة على المنصة
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            type="button"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700"
          >
            <PlusIcon className="h-4 w-4 ml-2" />
            إضافة خدمة جديدة
          </button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {services.filter(s => s.status === 'active').length}
                  </span>
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    خدمات نشطة
                  </dt>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {services.filter(s => s.status === 'pending').length}
                  </span>
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    في الانتظار
                  </dt>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {services.reduce((sum, s) => sum + s.orderCount, 0)}
                  </span>
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الطلبات
                  </dt>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {services.reduce((sum, s) => sum + s.views, 0)}
                  </span>
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي المشاهدات
                  </dt>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            تصفية حسب الحالة:
          </label>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="block border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="all">جميع الخدمات</option>
            <option value="active">نشط</option>
            <option value="pending">في الانتظار</option>
            <option value="rejected">مرفوض</option>
            <option value="draft">مسودة</option>
          </select>
        </div>
      </div>

      {/* Services grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredServices.map((service) => (
          <div key={service.id} className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white line-clamp-2">
                    {service.title}
                    {service.featured && (
                      <StarIcon className="inline h-4 w-4 text-yellow-400 mr-1" />
                    )}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {service.category}
                  </p>
                </div>
                <div className="mr-4">
                  {getStatusBadge(service.status)}
                </div>
              </div>

              {/* Description */}
              <p className="mt-3 text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                {service.description}
              </p>

              {/* Metrics */}
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <CurrencyDollarIcon className="h-4 w-4 ml-1" />
                  ${service.price}
                </div>
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <ClockIcon className="h-4 w-4 ml-1" />
                  {service.deliveryTime} أيام
                </div>
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <StarIcon className="h-4 w-4 ml-1" />
                  {service.rating} ({service.reviewCount})
                </div>
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <EyeIcon className="h-4 w-4 ml-1" />
                  {service.views} مشاهدة
                </div>
              </div>

              {/* Performance stats */}
              <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">{service.orderCount}</span> طلب مكتمل
                </div>
              </div>

              {/* Actions */}
              <div className="mt-6 flex space-x-3 rtl:space-x-reverse">
                <button className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                  <EyeIcon className="h-4 w-4 ml-2" />
                  عرض
                </button>
                <button className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                  <PencilIcon className="h-4 w-4 ml-2" />
                  تعديل
                </button>
                <button 
                  onClick={() => handleDelete(service.id)}
                  className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  <TrashIcon className="h-4 w-4 ml-2" />
                  حذف
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {filteredServices.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 dark:text-gray-600">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">لا توجد خدمات</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">ابدأ بإنشاء خدمتك الأولى.</p>
          <div className="mt-6">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <PlusIcon className="h-4 w-4 ml-2" />
              إضافة خدمة جديدة
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

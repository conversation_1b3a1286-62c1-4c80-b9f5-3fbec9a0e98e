'use client';

import { useState } from 'react';
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  UsersIcon,
  CurrencyDollarIcon,
  BriefcaseIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';

const userGrowthData = [
  { month: 'يناير', users: 1200, experts: 300, clients: 900 },
  { month: 'فبراير', users: 1450, experts: 380, clients: 1070 },
  { month: 'مارس', users: 1680, experts: 420, clients: 1260 },
  { month: 'أبريل', users: 1920, experts: 480, clients: 1440 },
  { month: 'مايو', users: 2150, experts: 540, clients: 1610 },
  { month: 'يونيو', users: 2400, experts: 600, clients: 1800 },
];

const revenueData = [
  { month: 'يناير', revenue: 15000, commission: 3000 },
  { month: 'فبراير', revenue: 18500, commission: 3700 },
  { month: 'مارس', revenue: 22000, commission: 4400 },
  { month: 'أبريل', revenue: 26500, commission: 5300 },
  { month: 'مايو', revenue: 31000, commission: 6200 },
  { month: 'يونيو', revenue: 35500, commission: 7100 },
];

const categoryData = [
  { name: 'تطوير الويب', value: 35, color: '#3B82F6' },
  { name: 'تطوير التطبيقات', value: 25, color: '#10B981' },
  { name: 'التصميم', value: 20, color: '#F59E0B' },
  { name: 'التسويق', value: 12, color: '#EF4444' },
  { name: 'أخرى', value: 8, color: '#8B5CF6' },
];

const topExpertsData = [
  { name: 'أحمد محمد', bookings: 45, revenue: 12500 },
  { name: 'فاطمة أحمد', bookings: 38, revenue: 9800 },
  { name: 'محمد علي', bookings: 32, revenue: 8200 },
  { name: 'سارة خالد', bookings: 28, revenue: 7100 },
  { name: 'عمر حسن', bookings: 25, revenue: 6500 },
];

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('6months');

  const stats = [
    {
      name: 'إجمالي المستخدمين',
      value: '2,847',
      change: '+12.5%',
      changeType: 'increase',
      icon: UsersIcon,
    },
    {
      name: 'الإيرادات الشهرية',
      value: '$35,500',
      change: '+18.2%',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'الخدمات النشطة',
      value: '1,234',
      change: '+8.1%',
      changeType: 'increase',
      icon: BriefcaseIcon,
    },
    {
      name: 'معدل التحويل',
      value: '3.2%',
      change: '-2.1%',
      changeType: 'decrease',
      icon: ChartBarIcon,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            التحليلات والإحصائيات
          </h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            نظرة شاملة على أداء المنصة والمقاييس الرئيسية
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="7days">آخر 7 أيام</option>
            <option value="30days">آخر 30 يوم</option>
            <option value="3months">آخر 3 أشهر</option>
            <option value="6months">آخر 6 أشهر</option>
            <option value="1year">آخر سنة</option>
          </select>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div
            key={stat.name}
            className="relative overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:px-6"
          >
            <dt>
              <div className="absolute rounded-md bg-primary-500 p-3">
                <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              <p className="mr-16 rtl:mr-0 rtl:ml-16 truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                {stat.name}
              </p>
            </dt>
            <dd className="mr-16 rtl:mr-0 rtl:ml-16 flex items-baseline pb-6 sm:pb-7">
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stat.value}
              </p>
              <p
                className={`mr-2 flex items-baseline text-sm font-semibold ${
                  stat.changeType === 'increase'
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}
              >
                {stat.changeType === 'increase' ? (
                  <ArrowUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                )}
                <span className="sr-only">
                  {stat.changeType === 'increase' ? 'زيادة' : 'نقصان'} بنسبة
                </span>
                {stat.change}
              </p>
            </dd>
          </div>
        ))}
      </div>

      {/* Charts grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            نمو المستخدمين
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={userGrowthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="users" 
                stackId="1" 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                name="إجمالي المستخدمين"
              />
              <Area 
                type="monotone" 
                dataKey="experts" 
                stackId="2" 
                stroke="#10B981" 
                fill="#10B981" 
                name="الخبراء"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Revenue Chart */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            الإيرادات والعمولات
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#3B82F6" 
                strokeWidth={3}
                name="إجمالي الإيرادات"
              />
              <Line 
                type="monotone" 
                dataKey="commission" 
                stroke="#10B981" 
                strokeWidth={3}
                name="عمولة المنصة"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Additional analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Category Distribution */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            توزيع الفئات
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Top Experts */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            أفضل الخبراء أداءً
          </h3>
          <div className="space-y-4">
            {topExpertsData.map((expert, index) => (
              <div key={expert.name} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center">
                      <span className="text-sm font-medium text-white">
                        {expert.name.charAt(0)}
                      </span>
                    </div>
                  </div>
                  <div className="mr-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {expert.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {expert.bookings} حجز مكتمل
                    </div>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  ${expert.revenue.toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance metrics */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          مقاييس الأداء الرئيسية
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              68%
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              معدل إكمال الحجوزات
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              4.7
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              متوسط التقييم
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              2.3h
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              متوسط وقت الاستجابة
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              89%
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              رضا العملاء
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

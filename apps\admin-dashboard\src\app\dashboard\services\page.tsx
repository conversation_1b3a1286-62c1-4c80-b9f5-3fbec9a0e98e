'use client';

import { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  CheckIcon,
  XMarkIcon,
  EyeIcon,
  PencilIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface Service {
  id: string;
  title: string;
  description: string;
  category: string;
  expert: {
    name: string;
    avatar?: string;
  };
  price: number;
  status: 'active' | 'pending' | 'rejected' | 'suspended';
  featured: boolean;
  rating: number;
  reviewCount: number;
  createdAt: string;
}

const mockServices: Service[] = [
  {
    id: '1',
    title: 'تصميم مواقع الويب الاحترافية',
    description: 'تصميم وتطوير مواقع ويب حديثة ومتجاوبة باستخدام أحدث التقنيات',
    category: 'تطوير الويب',
    expert: {
      name: 'أحمد محمد',
    },
    price: 500,
    status: 'active',
    featured: true,
    rating: 4.8,
    reviewCount: 24,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    title: 'تطوير تطبيقات الموبايل',
    description: 'تطوير تطبيقات iOS و Android باستخدام React Native',
    category: 'تطوير التطبيقات',
    expert: {
      name: 'فاطمة أحمد',
    },
    price: 800,
    status: 'pending',
    featured: false,
    rating: 4.9,
    reviewCount: 18,
    createdAt: '2024-01-20',
  },
  {
    id: '3',
    title: 'استشارات تقنية متخصصة',
    description: 'استشارات في مجال التكنولوجيا والحلول الرقمية',
    category: 'استشارات',
    expert: {
      name: 'محمد علي',
    },
    price: 100,
    status: 'rejected',
    featured: false,
    rating: 4.5,
    reviewCount: 12,
    createdAt: '2024-01-18',
  },
];

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>(mockServices);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredServices = services.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.expert.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || service.status === selectedStatus;
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      suspended: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    };
    
    const labels = {
      active: 'نشط',
      pending: 'في الانتظار',
      rejected: 'مرفوض',
      suspended: 'معلق',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  const handleApprove = (serviceId: string) => {
    setServices(services.map(service => 
      service.id === serviceId ? { ...service, status: 'active' as const } : service
    ));
  };

  const handleReject = (serviceId: string) => {
    setServices(services.map(service => 
      service.id === serviceId ? { ...service, status: 'rejected' as const } : service
    ));
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            إدارة الخدمات
          </h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            إدارة جميع الخدمات المنشورة على المنصة مع إمكانية الموافقة والرفض والتعديل
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث عن خدمة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* Status filter */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="pending">في الانتظار</option>
              <option value="rejected">مرفوض</option>
              <option value="suspended">معلق</option>
            </select>
          </div>

          {/* Category filter */}
          <div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="block w-full border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">جميع الفئات</option>
              <option value="تطوير الويب">تطوير الويب</option>
              <option value="تطوير التطبيقات">تطوير التطبيقات</option>
              <option value="استشارات">استشارات</option>
              <option value="تصميم">تصميم</option>
            </select>
          </div>
        </div>
      </div>

      {/* Services grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredServices.map((service) => (
          <div key={service.id} className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white line-clamp-2">
                    {service.title}
                    {service.featured && (
                      <StarIcon className="inline h-4 w-4 text-yellow-400 mr-1" />
                    )}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {service.category}
                  </p>
                </div>
                <div className="mr-4">
                  {getStatusBadge(service.status)}
                </div>
              </div>

              {/* Description */}
              <p className="mt-3 text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                {service.description}
              </p>

              {/* Expert info */}
              <div className="mt-4 flex items-center">
                <div className="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">
                    {service.expert.name.charAt(0)}
                  </span>
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {service.expert.name}
                  </p>
                </div>
              </div>

              {/* Rating and price */}
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center">
                  <StarIcon className="h-4 w-4 text-yellow-400" />
                  <span className="mr-1 text-sm text-gray-600 dark:text-gray-300">
                    {service.rating} ({service.reviewCount})
                  </span>
                </div>
                <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
                  ${service.price}
                </div>
              </div>

              {/* Actions */}
              <div className="mt-6 flex space-x-3 rtl:space-x-reverse">
                <button className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                  <EyeIcon className="h-4 w-4 ml-2" />
                  عرض
                </button>
                
                {service.status === 'pending' && (
                  <>
                    <button 
                      onClick={() => handleApprove(service.id)}
                      className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                    >
                      <CheckIcon className="h-4 w-4 ml-2" />
                      موافقة
                    </button>
                    <button 
                      onClick={() => handleReject(service.id)}
                      className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                    >
                      <XMarkIcon className="h-4 w-4 ml-2" />
                      رفض
                    </button>
                  </>
                )}
                
                {service.status !== 'pending' && (
                  <button className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                    <PencilIcon className="h-4 w-4 ml-2" />
                    تعديل
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {filteredServices.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 dark:text-gray-600">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">لا توجد خدمات</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم العثور على خدمات تطابق معايير البحث.</p>
        </div>
      )}
    </div>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-format";
exports.ids = ["vendor-chunks/d3-format"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-format/src/defaultLocale.js":
/*!*********************************************************!*\
  !*** ../../node_modules/d3-format/src/defaultLocale.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultLocale),\n/* harmony export */   format: () => (/* binding */ format),\n/* harmony export */   formatPrefix: () => (/* binding */ formatPrefix)\n/* harmony export */ });\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/../../node_modules/d3-format/src/locale.js\");\n\nvar locale;\nvar format;\nvar formatPrefix;\ndefaultLocale({\n    thousands: \",\",\n    grouping: [\n        3\n    ],\n    currency: [\n        \"$\",\n        \"\"\n    ]\n});\nfunction defaultLocale(definition) {\n    locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(definition);\n    format = locale.format;\n    formatPrefix = locale.formatPrefix;\n    return locale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZGVmYXVsdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVDO0FBRXZDLElBQUlDO0FBQ0csSUFBSUMsT0FBTztBQUNYLElBQUlDLGFBQWE7QUFFeEJDLGNBQWM7SUFDWkMsV0FBVztJQUNYQyxVQUFVO1FBQUM7S0FBRTtJQUNiQyxVQUFVO1FBQUM7UUFBSztLQUFHO0FBQ3JCO0FBRWUsU0FBU0gsY0FBY0ksVUFBVTtJQUM5Q1AsU0FBU0Qsc0RBQVlBLENBQUNRO0lBQ3RCTixTQUFTRCxPQUFPQyxNQUFNO0lBQ3RCQyxlQUFlRixPQUFPRSxZQUFZO0lBQ2xDLE9BQU9GO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9kZWZhdWx0TG9jYWxlLmpzPzlmM2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGZvcm1hdExvY2FsZSBmcm9tIFwiLi9sb2NhbGUuanNcIjtcblxudmFyIGxvY2FsZTtcbmV4cG9ydCB2YXIgZm9ybWF0O1xuZXhwb3J0IHZhciBmb3JtYXRQcmVmaXg7XG5cbmRlZmF1bHRMb2NhbGUoe1xuICB0aG91c2FuZHM6IFwiLFwiLFxuICBncm91cGluZzogWzNdLFxuICBjdXJyZW5jeTogW1wiJFwiLCBcIlwiXVxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRlZmF1bHRMb2NhbGUoZGVmaW5pdGlvbikge1xuICBsb2NhbGUgPSBmb3JtYXRMb2NhbGUoZGVmaW5pdGlvbik7XG4gIGZvcm1hdCA9IGxvY2FsZS5mb3JtYXQ7XG4gIGZvcm1hdFByZWZpeCA9IGxvY2FsZS5mb3JtYXRQcmVmaXg7XG4gIHJldHVybiBsb2NhbGU7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TG9jYWxlIiwibG9jYWxlIiwiZm9ybWF0IiwiZm9ybWF0UHJlZml4IiwiZGVmYXVsdExvY2FsZSIsInRob3VzYW5kcyIsImdyb3VwaW5nIiwiY3VycmVuY3kiLCJkZWZpbml0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/defaultLocale.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/exponent.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-format/src/exponent.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../../node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return x = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(Math.abs(x)), x ? x[1] : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZXhwb25lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFFdEQsNkJBQWUsb0NBQVNDLENBQUM7SUFDdkIsT0FBT0EsSUFBSUQscUVBQWtCQSxDQUFDRSxLQUFLQyxHQUFHLENBQUNGLEtBQUtBLElBQUlBLENBQUMsQ0FBQyxFQUFFLEdBQUdHO0FBQ3pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9hZG1pbi1kYXNoYm9hcmQvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZXhwb25lbnQuanM/ZDc4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvcm1hdERlY2ltYWxQYXJ0c30gZnJvbSBcIi4vZm9ybWF0RGVjaW1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4KSB7XG4gIHJldHVybiB4ID0gZm9ybWF0RGVjaW1hbFBhcnRzKE1hdGguYWJzKHgpKSwgeCA/IHhbMV0gOiBOYU47XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0RGVjaW1hbFBhcnRzIiwieCIsIk1hdGgiLCJhYnMiLCJOYU4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/exponent.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatDecimal.js":
/*!*********************************************************!*\
  !*** ../../node_modules/d3-format/src/formatDecimal.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatDecimalParts: () => (/* binding */ formatDecimalParts)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n}\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nfunction formatDecimalParts(x, p) {\n    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n    var i, coefficient = x.slice(0, i);\n    // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n    // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n    return [\n        coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n        +x.slice(i + 1)\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatDecimal.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatGroup.js":
/*!*******************************************************!*\
  !*** ../../node_modules/d3-format/src/formatGroup.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(grouping, thousands) {\n    return function(value, width) {\n        var i = value.length, t = [], j = 0, g = grouping[0], length = 0;\n        while(i > 0 && g > 0){\n            if (length + g + 1 > width) g = Math.max(1, width - length);\n            t.push(value.substring(i -= g, i + g));\n            if ((length += g + 1) > width) break;\n            g = grouping[j = (j + 1) % grouping.length];\n        }\n        return t.reverse().join(thousands);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0R3JvdXAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxRQUFRLEVBQUVDLFNBQVM7SUFDekMsT0FBTyxTQUFTQyxLQUFLLEVBQUVDLEtBQUs7UUFDMUIsSUFBSUMsSUFBSUYsTUFBTUcsTUFBTSxFQUNoQkMsSUFBSSxFQUFFLEVBQ05DLElBQUksR0FDSkMsSUFBSVIsUUFBUSxDQUFDLEVBQUUsRUFDZkssU0FBUztRQUViLE1BQU9ELElBQUksS0FBS0ksSUFBSSxFQUFHO1lBQ3JCLElBQUlILFNBQVNHLElBQUksSUFBSUwsT0FBT0ssSUFBSUMsS0FBS0MsR0FBRyxDQUFDLEdBQUdQLFFBQVFFO1lBQ3BEQyxFQUFFSyxJQUFJLENBQUNULE1BQU1VLFNBQVMsQ0FBQ1IsS0FBS0ksR0FBR0osSUFBSUk7WUFDbkMsSUFBSSxDQUFDSCxVQUFVRyxJQUFJLEtBQUtMLE9BQU87WUFDL0JLLElBQUlSLFFBQVEsQ0FBQ08sSUFBSSxDQUFDQSxJQUFJLEtBQUtQLFNBQVNLLE1BQU0sQ0FBQztRQUM3QztRQUVBLE9BQU9DLEVBQUVPLE9BQU8sR0FBR0MsSUFBSSxDQUFDYjtJQUMxQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9hZG1pbi1kYXNoYm9hcmQvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0R3JvdXAuanM/ZTM1NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihncm91cGluZywgdGhvdXNhbmRzKSB7XG4gIHJldHVybiBmdW5jdGlvbih2YWx1ZSwgd2lkdGgpIHtcbiAgICB2YXIgaSA9IHZhbHVlLmxlbmd0aCxcbiAgICAgICAgdCA9IFtdLFxuICAgICAgICBqID0gMCxcbiAgICAgICAgZyA9IGdyb3VwaW5nWzBdLFxuICAgICAgICBsZW5ndGggPSAwO1xuXG4gICAgd2hpbGUgKGkgPiAwICYmIGcgPiAwKSB7XG4gICAgICBpZiAobGVuZ3RoICsgZyArIDEgPiB3aWR0aCkgZyA9IE1hdGgubWF4KDEsIHdpZHRoIC0gbGVuZ3RoKTtcbiAgICAgIHQucHVzaCh2YWx1ZS5zdWJzdHJpbmcoaSAtPSBnLCBpICsgZykpO1xuICAgICAgaWYgKChsZW5ndGggKz0gZyArIDEpID4gd2lkdGgpIGJyZWFrO1xuICAgICAgZyA9IGdyb3VwaW5nW2ogPSAoaiArIDEpICUgZ3JvdXBpbmcubGVuZ3RoXTtcbiAgICB9XG5cbiAgICByZXR1cm4gdC5yZXZlcnNlKCkuam9pbih0aG91c2FuZHMpO1xuICB9O1xufVxuIl0sIm5hbWVzIjpbImdyb3VwaW5nIiwidGhvdXNhbmRzIiwidmFsdWUiLCJ3aWR0aCIsImkiLCJsZW5ndGgiLCJ0IiwiaiIsImciLCJNYXRoIiwibWF4IiwicHVzaCIsInN1YnN0cmluZyIsInJldmVyc2UiLCJqb2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatGroup.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatNumerals.js":
/*!**********************************************************!*\
  !*** ../../node_modules/d3-format/src/formatNumerals.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(numerals) {\n    return function(value) {\n        return value.replace(/[0-9]/g, function(i) {\n            return numerals[+i];\n        });\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0TnVtZXJhbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxRQUFRO0lBQzlCLE9BQU8sU0FBU0MsS0FBSztRQUNuQixPQUFPQSxNQUFNQyxPQUFPLENBQUMsVUFBVSxTQUFTQyxDQUFDO1lBQ3ZDLE9BQU9ILFFBQVEsQ0FBQyxDQUFDRyxFQUFFO1FBQ3JCO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvYWRtaW4tZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2Zvcm1hdE51bWVyYWxzLmpzP2Q0Y2UiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24obnVtZXJhbHMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlLnJlcGxhY2UoL1swLTldL2csIGZ1bmN0aW9uKGkpIHtcbiAgICAgIHJldHVybiBudW1lcmFsc1sraV07XG4gICAgfSk7XG4gIH07XG59XG4iXSwibmFtZXMiOlsibnVtZXJhbHMiLCJ2YWx1ZSIsInJlcGxhY2UiLCJpIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatNumerals.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatPrefixAuto.js":
/*!************************************************************!*\
  !*** ../../node_modules/d3-format/src/formatPrefixAuto.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prefixExponent: () => (/* binding */ prefixExponent)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../../node_modules/d3-format/src/formatDecimal.js\");\n\nvar prefixExponent;\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n    var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n    if (!d) return x + \"\";\n    var coefficient = d[0], exponent = d[1], i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n = coefficient.length;\n    return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatPrefixAuto.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatRounded.js":
/*!*********************************************************!*\
  !*** ../../node_modules/d3-format/src/formatRounded.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../../node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n    var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n    if (!d) return x + \"\";\n    var coefficient = d[0], exponent = d[1];\n    return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0Um91bmRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUV0RCw2QkFBZSxvQ0FBU0MsQ0FBQyxFQUFFQyxDQUFDO0lBQzFCLElBQUlDLElBQUlILHFFQUFrQkEsQ0FBQ0MsR0FBR0M7SUFDOUIsSUFBSSxDQUFDQyxHQUFHLE9BQU9GLElBQUk7SUFDbkIsSUFBSUcsY0FBY0QsQ0FBQyxDQUFDLEVBQUUsRUFDbEJFLFdBQVdGLENBQUMsQ0FBQyxFQUFFO0lBQ25CLE9BQU9FLFdBQVcsSUFBSSxPQUFPLElBQUlDLE1BQU0sQ0FBQ0QsVUFBVUUsSUFBSSxDQUFDLE9BQU9ILGNBQ3hEQSxZQUFZSSxNQUFNLEdBQUdILFdBQVcsSUFBSUQsWUFBWUssS0FBSyxDQUFDLEdBQUdKLFdBQVcsS0FBSyxNQUFNRCxZQUFZSyxLQUFLLENBQUNKLFdBQVcsS0FDNUdELGNBQWMsSUFBSUUsTUFBTUQsV0FBV0QsWUFBWUksTUFBTSxHQUFHLEdBQUdELElBQUksQ0FBQztBQUN4RSIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvYWRtaW4tZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2Zvcm1hdFJvdW5kZWQuanM/Y2NiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvcm1hdERlY2ltYWxQYXJ0c30gZnJvbSBcIi4vZm9ybWF0RGVjaW1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4LCBwKSB7XG4gIHZhciBkID0gZm9ybWF0RGVjaW1hbFBhcnRzKHgsIHApO1xuICBpZiAoIWQpIHJldHVybiB4ICsgXCJcIjtcbiAgdmFyIGNvZWZmaWNpZW50ID0gZFswXSxcbiAgICAgIGV4cG9uZW50ID0gZFsxXTtcbiAgcmV0dXJuIGV4cG9uZW50IDwgMCA/IFwiMC5cIiArIG5ldyBBcnJheSgtZXhwb25lbnQpLmpvaW4oXCIwXCIpICsgY29lZmZpY2llbnRcbiAgICAgIDogY29lZmZpY2llbnQubGVuZ3RoID4gZXhwb25lbnQgKyAxID8gY29lZmZpY2llbnQuc2xpY2UoMCwgZXhwb25lbnQgKyAxKSArIFwiLlwiICsgY29lZmZpY2llbnQuc2xpY2UoZXhwb25lbnQgKyAxKVxuICAgICAgOiBjb2VmZmljaWVudCArIG5ldyBBcnJheShleHBvbmVudCAtIGNvZWZmaWNpZW50Lmxlbmd0aCArIDIpLmpvaW4oXCIwXCIpO1xufVxuIl0sIm5hbWVzIjpbImZvcm1hdERlY2ltYWxQYXJ0cyIsIngiLCJwIiwiZCIsImNvZWZmaWNpZW50IiwiZXhwb25lbnQiLCJBcnJheSIsImpvaW4iLCJsZW5ndGgiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatRounded.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatSpecifier.js":
/*!***********************************************************!*\
  !*** ../../node_modules/d3-format/src/formatSpecifier.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormatSpecifier: () => (/* binding */ FormatSpecifier),\n/* harmony export */   \"default\": () => (/* binding */ formatSpecifier)\n/* harmony export */ });\n// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\nfunction formatSpecifier(specifier) {\n    if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n    var match;\n    return new FormatSpecifier({\n        fill: match[1],\n        align: match[2],\n        sign: match[3],\n        symbol: match[4],\n        zero: match[5],\n        width: match[6],\n        comma: match[7],\n        precision: match[8] && match[8].slice(1),\n        trim: match[9],\n        type: match[10]\n    });\n}\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\nfunction FormatSpecifier(specifier) {\n    this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n    this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n    this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n    this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n    this.zero = !!specifier.zero;\n    this.width = specifier.width === undefined ? undefined : +specifier.width;\n    this.comma = !!specifier.comma;\n    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n    this.trim = !!specifier.trim;\n    this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\nFormatSpecifier.prototype.toString = function() {\n    return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatSpecifier.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatTrim.js":
/*!******************************************************!*\
  !*** ../../node_modules/d3-format/src/formatTrim.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(s) {\n    out: for(var n = s.length, i = 1, i0 = -1, i1; i < n; ++i){\n        switch(s[i]){\n            case \".\":\n                i0 = i1 = i;\n                break;\n            case \"0\":\n                if (i0 === 0) i0 = i;\n                i1 = i;\n                break;\n            default:\n                if (!+s[i]) break out;\n                if (i0 > 0) i0 = 0;\n                break;\n        }\n    }\n    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0VHJpbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsK0RBQStEO0FBQy9ELDZCQUFlLG9DQUFTQSxDQUFDO0lBQ3ZCQyxLQUFLLElBQUssSUFBSUMsSUFBSUYsRUFBRUcsTUFBTSxFQUFFQyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxHQUFHQyxJQUFJRixJQUFJRixHQUFHLEVBQUVFLEVBQUc7UUFDMUQsT0FBUUosQ0FBQyxDQUFDSSxFQUFFO1lBQ1YsS0FBSztnQkFBS0MsS0FBS0MsS0FBS0Y7Z0JBQUc7WUFDdkIsS0FBSztnQkFBSyxJQUFJQyxPQUFPLEdBQUdBLEtBQUtEO2dCQUFHRSxLQUFLRjtnQkFBRztZQUN4QztnQkFBUyxJQUFJLENBQUMsQ0FBQ0osQ0FBQyxDQUFDSSxFQUFFLEVBQUUsTUFBTUg7Z0JBQUssSUFBSUksS0FBSyxHQUFHQSxLQUFLO2dCQUFHO1FBQ3REO0lBQ0Y7SUFDQSxPQUFPQSxLQUFLLElBQUlMLEVBQUVPLEtBQUssQ0FBQyxHQUFHRixNQUFNTCxFQUFFTyxLQUFLLENBQUNELEtBQUssS0FBS047QUFDckQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRUcmltLmpzP2ZlZjgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVHJpbXMgaW5zaWduaWZpY2FudCB6ZXJvcywgZS5nLiwgcmVwbGFjZXMgMS4yMDAwayB3aXRoIDEuMmsuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzKSB7XG4gIG91dDogZm9yICh2YXIgbiA9IHMubGVuZ3RoLCBpID0gMSwgaTAgPSAtMSwgaTE7IGkgPCBuOyArK2kpIHtcbiAgICBzd2l0Y2ggKHNbaV0pIHtcbiAgICAgIGNhc2UgXCIuXCI6IGkwID0gaTEgPSBpOyBicmVhaztcbiAgICAgIGNhc2UgXCIwXCI6IGlmIChpMCA9PT0gMCkgaTAgPSBpOyBpMSA9IGk7IGJyZWFrO1xuICAgICAgZGVmYXVsdDogaWYgKCErc1tpXSkgYnJlYWsgb3V0OyBpZiAoaTAgPiAwKSBpMCA9IDA7IGJyZWFrO1xuICAgIH1cbiAgfVxuICByZXR1cm4gaTAgPiAwID8gcy5zbGljZSgwLCBpMCkgKyBzLnNsaWNlKGkxICsgMSkgOiBzO1xufVxuIl0sIm5hbWVzIjpbInMiLCJvdXQiLCJuIiwibGVuZ3RoIiwiaSIsImkwIiwiaTEiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatTrim.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/formatTypes.js":
/*!*******************************************************!*\
  !*** ../../node_modules/d3-format/src/formatTypes.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../../node_modules/d3-format/src/formatDecimal.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/../../node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatRounded.js */ \"(ssr)/../../node_modules/d3-format/src/formatRounded.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    \"%\": (x, p)=>(x * 100).toFixed(p),\n    \"b\": (x)=>Math.round(x).toString(2),\n    \"c\": (x)=>x + \"\",\n    \"d\": _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    \"e\": (x, p)=>x.toExponential(p),\n    \"f\": (x, p)=>x.toFixed(p),\n    \"g\": (x, p)=>x.toPrecision(p),\n    \"o\": (x)=>Math.round(x).toString(8),\n    \"p\": (x, p)=>(0,_formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(x * 100, p),\n    \"r\": _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    \"s\": _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    \"X\": (x)=>Math.round(x).toString(16).toUpperCase(),\n    \"x\": (x)=>Math.round(x).toString(16)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/formatTypes.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/identity.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-format/src/identity.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvaWRlbnRpdHkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxDQUFDO0lBQ3ZCLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9pZGVudGl0eS5qcz81MzdlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHgpIHtcbiAgcmV0dXJuIHg7XG59XG4iXSwibmFtZXMiOlsieCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/identity.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/locale.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-format/src/locale.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../../node_modules/d3-format/src/exponent.js\");\n/* harmony import */ var _formatGroup_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatGroup.js */ \"(ssr)/../../node_modules/d3-format/src/formatGroup.js\");\n/* harmony import */ var _formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatNumerals.js */ \"(ssr)/../../node_modules/d3-format/src/formatNumerals.js\");\n/* harmony import */ var _formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatSpecifier.js */ \"(ssr)/../../node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var _formatTrim_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./formatTrim.js */ \"(ssr)/../../node_modules/d3-format/src/formatTrim.js\");\n/* harmony import */ var _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./formatTypes.js */ \"(ssr)/../../node_modules/d3-format/src/formatTypes.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/../../node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/../../node_modules/d3-format/src/identity.js\");\n\n\n\n\n\n\n\n\nvar map = Array.prototype.map, prefixes = [\n    \"y\",\n    \"z\",\n    \"a\",\n    \"f\",\n    \"p\",\n    \"n\",\n    \"\\xb5\",\n    \"m\",\n    \"\",\n    \"k\",\n    \"M\",\n    \"G\",\n    \"T\",\n    \"P\",\n    \"E\",\n    \"Z\",\n    \"Y\"\n];\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(locale) {\n    var group = locale.grouping === undefined || locale.thousands === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatGroup_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(map.call(locale.grouping, Number), locale.thousands + \"\"), currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\", currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\", decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\", numerals = locale.numerals === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(map.call(locale.numerals, String)), percent = locale.percent === undefined ? \"%\" : locale.percent + \"\", minus = locale.minus === undefined ? \"−\" : locale.minus + \"\", nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n    function newFormat(specifier) {\n        specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier);\n        var fill = specifier.fill, align = specifier.align, sign = specifier.sign, symbol = specifier.symbol, zero = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;\n        // The \"n\" type is an alias for \",g\".\n        if (type === \"n\") comma = true, type = \"g\";\n        else if (!_formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n        // If zero fill is specified, padding goes after sign and before digits.\n        if (zero || fill === \"0\" && align === \"=\") zero = true, fill = \"0\", align = \"=\";\n        // Compute the prefix and suffix.\n        // For SI-prefix, the suffix is lazily computed.\n        var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\", suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n        // What format function should we use?\n        // Is this an integer type?\n        // Can this type generate exponential notation?\n        var formatType = _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type], maybeSuffix = /[defgprs%]/.test(type);\n        // Set the default precision if not specified,\n        // or clamp the specified precision to the supported range.\n        // For significant precision, it must be in [1, 21].\n        // For fixed precision, it must be in [0, 20].\n        precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));\n        function format(value) {\n            var valuePrefix = prefix, valueSuffix = suffix, i, n, c;\n            if (type === \"c\") {\n                valueSuffix = formatType(value) + valueSuffix;\n                value = \"\";\n            } else {\n                value = +value;\n                // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n                var valueNegative = value < 0 || 1 / value < 0;\n                // Perform the initial formatting.\n                value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n                // Trim insignificant zeros.\n                if (trim) value = (0,_formatTrim_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(value);\n                // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n                if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n                // Compute the prefix and suffix.\n                valuePrefix = (valueNegative ? sign === \"(\" ? sign : minus : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n                valueSuffix = (type === \"s\" ? prefixes[8 + _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__.prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n                // Break the formatted value into the integer “value” part that can be\n                // grouped, and fractional or exponential “suffix” part that is not.\n                if (maybeSuffix) {\n                    i = -1, n = value.length;\n                    while(++i < n){\n                        if (c = value.charCodeAt(i), 48 > c || c > 57) {\n                            valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n                            value = value.slice(0, i);\n                            break;\n                        }\n                    }\n                }\n            }\n            // If the fill character is not \"0\", grouping is applied before padding.\n            if (comma && !zero) value = group(value, Infinity);\n            // Compute the padding.\n            var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n            // If the fill character is \"0\", grouping is applied after padding.\n            if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n            // Reconstruct the final output based on the desired alignment.\n            switch(align){\n                case \"<\":\n                    value = valuePrefix + value + valueSuffix + padding;\n                    break;\n                case \"=\":\n                    value = valuePrefix + padding + value + valueSuffix;\n                    break;\n                case \"^\":\n                    value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);\n                    break;\n                default:\n                    value = padding + valuePrefix + value + valueSuffix;\n                    break;\n            }\n            return numerals(value);\n        }\n        format.toString = function() {\n            return specifier + \"\";\n        };\n        return format;\n    }\n    function formatPrefix(specifier, value) {\n        var f = newFormat((specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier), specifier.type = \"f\", specifier)), e = Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value) / 3))) * 3, k = Math.pow(10, -e), prefix = prefixes[8 + e / 3];\n        return function(value) {\n            return f(k * value) + prefix;\n        };\n    }\n    return {\n        format: newFormat,\n        formatPrefix: formatPrefix\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/locale.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/precisionFixed.js":
/*!**********************************************************!*\
  !*** ../../node_modules/d3-format/src/precisionFixed.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../../node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step) {\n    return Math.max(0, -(0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvcHJlY2lzaW9uRml4ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFckMsNkJBQWUsb0NBQVNDLElBQUk7SUFDMUIsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLEdBQUcsQ0FBQ0gsd0RBQVFBLENBQUNFLEtBQUtFLEdBQUcsQ0FBQ0g7QUFDeEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25GaXhlZC5qcz9iMjNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBleHBvbmVudCBmcm9tIFwiLi9leHBvbmVudC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzdGVwKSB7XG4gIHJldHVybiBNYXRoLm1heCgwLCAtZXhwb25lbnQoTWF0aC5hYnMoc3RlcCkpKTtcbn1cbiJdLCJuYW1lcyI6WyJleHBvbmVudCIsInN0ZXAiLCJNYXRoIiwibWF4IiwiYWJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/precisionFixed.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/precisionPrefix.js":
/*!***********************************************************!*\
  !*** ../../node_modules/d3-format/src/precisionPrefix.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../../node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, value) {\n    return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value) / 3))) * 3 - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvcHJlY2lzaW9uUHJlZml4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFDO0FBRXJDLDZCQUFlLG9DQUFTQyxJQUFJLEVBQUVDLEtBQUs7SUFDakMsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtDLEdBQUcsQ0FBQyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxHQUFHRixLQUFLRyxLQUFLLENBQUNOLHdEQUFRQSxDQUFDRSxTQUFTLE9BQU8sSUFBSUYsd0RBQVFBLENBQUNHLEtBQUtJLEdBQUcsQ0FBQ047QUFDeEciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25QcmVmaXguanM/MWViYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXhwb25lbnQgZnJvbSBcIi4vZXhwb25lbnQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oc3RlcCwgdmFsdWUpIHtcbiAgcmV0dXJuIE1hdGgubWF4KDAsIE1hdGgubWF4KC04LCBNYXRoLm1pbig4LCBNYXRoLmZsb29yKGV4cG9uZW50KHZhbHVlKSAvIDMpKSkgKiAzIC0gZXhwb25lbnQoTWF0aC5hYnMoc3RlcCkpKTtcbn1cbiJdLCJuYW1lcyI6WyJleHBvbmVudCIsInN0ZXAiLCJ2YWx1ZSIsIk1hdGgiLCJtYXgiLCJtaW4iLCJmbG9vciIsImFicyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/precisionPrefix.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-format/src/precisionRound.js":
/*!**********************************************************!*\
  !*** ../../node_modules/d3-format/src/precisionRound.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../../node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, max) {\n    step = Math.abs(step), max = Math.abs(max) - step;\n    return Math.max(0, (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(max) - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(step)) + 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvcHJlY2lzaW9uUm91bmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFckMsNkJBQWUsb0NBQVNDLElBQUksRUFBRUMsR0FBRztJQUMvQkQsT0FBT0UsS0FBS0MsR0FBRyxDQUFDSCxPQUFPQyxNQUFNQyxLQUFLQyxHQUFHLENBQUNGLE9BQU9EO0lBQzdDLE9BQU9FLEtBQUtELEdBQUcsQ0FBQyxHQUFHRix3REFBUUEsQ0FBQ0UsT0FBT0Ysd0RBQVFBLENBQUNDLFNBQVM7QUFDdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25Sb3VuZC5qcz8yNzJhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBleHBvbmVudCBmcm9tIFwiLi9leHBvbmVudC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzdGVwLCBtYXgpIHtcbiAgc3RlcCA9IE1hdGguYWJzKHN0ZXApLCBtYXggPSBNYXRoLmFicyhtYXgpIC0gc3RlcDtcbiAgcmV0dXJuIE1hdGgubWF4KDAsIGV4cG9uZW50KG1heCkgLSBleHBvbmVudChzdGVwKSkgKyAxO1xufVxuIl0sIm5hbWVzIjpbImV4cG9uZW50Iiwic3RlcCIsIm1heCIsIk1hdGgiLCJhYnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-format/src/precisionRound.js\n");

/***/ })

};
;
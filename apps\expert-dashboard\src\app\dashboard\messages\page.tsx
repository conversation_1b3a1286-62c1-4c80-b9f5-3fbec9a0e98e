'use client';

import { useState } from 'react';
import { 
  ChatBubbleLeftIcon,
  PaperAirplaneIcon,
  MagnifyingGlassIcon,
  UserIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  isRead: boolean;
}

interface Conversation {
  id: string;
  clientId: string;
  clientName: string;
  clientAvatar?: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  messages: Message[];
  bookingId?: string;
  serviceName?: string;
}

const mockConversations: Conversation[] = [
  {
    id: '1',
    clientId: 'client1',
    clientName: 'سارة أحمد',
    lastMessage: 'شكراً لك على الاستشارة المفيدة',
    lastMessageTime: '2024-01-22 15:30',
    unreadCount: 0,
    bookingId: 'BK001',
    serviceName: 'استشارة تطوير موقع إلكتروني',
    messages: [
      {
        id: 'm1',
        senderId: 'client1',
        senderName: 'سارة أحمد',
        content: 'مرحباً، أريد مناقشة متطلبات الموقع الإلكتروني',
        timestamp: '2024-01-22 14:00',
        isRead: true,
      },
      {
        id: 'm2',
        senderId: 'expert1',
        senderName: 'أحمد محمد',
        content: 'أهلاً وسهلاً، سأكون سعيداً لمساعدتك. ما هو نوع الموقع الذي تريدين إنشاءه؟',
        timestamp: '2024-01-22 14:05',
        isRead: true,
      },
      {
        id: 'm3',
        senderId: 'client1',
        senderName: 'سارة أحمد',
        content: 'شكراً لك على الاستشارة المفيدة',
        timestamp: '2024-01-22 15:30',
        isRead: true,
      },
    ],
  },
  {
    id: '2',
    clientId: 'client2',
    clientName: 'محمد علي',
    lastMessage: 'متى يمكننا بدء جلسة المراجعة؟',
    lastMessageTime: '2024-01-22 10:15',
    unreadCount: 2,
    bookingId: 'BK002',
    serviceName: 'مراجعة كود البرمجة',
    messages: [
      {
        id: 'm4',
        senderId: 'client2',
        senderName: 'محمد علي',
        content: 'مرحباً، لدي كود React.js أريد مراجعته',
        timestamp: '2024-01-22 09:30',
        isRead: true,
      },
      {
        id: 'm5',
        senderId: 'client2',
        senderName: 'محمد علي',
        content: 'متى يمكننا بدء جلسة المراجعة؟',
        timestamp: '2024-01-22 10:15',
        isRead: false,
      },
    ],
  },
  {
    id: '3',
    clientId: 'client3',
    clientName: 'نور حسن',
    lastMessage: 'هل يمكن تأجيل الجلسة لغداً؟',
    lastMessageTime: '2024-01-21 16:45',
    unreadCount: 1,
    bookingId: 'BK003',
    serviceName: 'تدريب على React Native',
    messages: [
      {
        id: 'm6',
        senderId: 'client3',
        senderName: 'نور حسن',
        content: 'هل يمكن تأجيل الجلسة لغداً؟',
        timestamp: '2024-01-21 16:45',
        isRead: false,
      },
    ],
  },
];

export default function MessagesPage() {
  const [conversations, setConversations] = useState<Conversation[]>(mockConversations);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const filteredConversations = conversations.filter(conv =>
    conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.serviceName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalUnreadCount = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);

  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    // Mark messages as read
    if (conversation.unreadCount > 0) {
      setConversations(prev => prev.map(conv =>
        conv.id === conversation.id
          ? { ...conv, unreadCount: 0, messages: conv.messages.map(msg => ({ ...msg, isRead: true })) }
          : conv
      ));
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;

    setIsLoading(true);
    try {
      const message: Message = {
        id: `m${Date.now()}`,
        senderId: 'expert1',
        senderName: 'أحمد محمد',
        content: newMessage.trim(),
        timestamp: new Date().toISOString(),
        isRead: true,
      };

      // Update conversations
      setConversations(prev => prev.map(conv =>
        conv.id === selectedConversation.id
          ? {
              ...conv,
              messages: [...conv.messages, message],
              lastMessage: message.content,
              lastMessageTime: message.timestamp,
            }
          : conv
      ));

      // Update selected conversation
      setSelectedConversation(prev => prev ? {
        ...prev,
        messages: [...prev.messages, message],
        lastMessage: message.content,
        lastMessageTime: message.timestamp,
      } : null);

      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'اليوم';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'أمس';
    } else {
      return date.toLocaleDateString('ar-SA');
    }
  };

  return (
    <div className="h-screen flex">
      {/* Conversations List */}
      <div className="w-1/3 bg-white dark:bg-gray-800 border-l rtl:border-l-0 rtl:border-r border-gray-200 dark:border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              الرسائل
            </h1>
            {totalUnreadCount > 0 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                {totalUnreadCount} غير مقروءة
              </span>
            )}
          </div>
          
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث في المحادثات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pr-10 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        {/* Conversations */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.length === 0 ? (
            <EmptyState
              icon={<ChatBubbleLeftIcon className="h-12 w-12" />}
              title="لا توجد محادثات"
              description="لم يتم العثور على محادثات تطابق البحث"
            />
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => handleSelectConversation(conversation)}
                  className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    selectedConversation?.id === conversation.id
                      ? 'bg-primary-50 dark:bg-primary-900 border-l-4 rtl:border-l-0 rtl:border-r-4 border-primary-500'
                      : ''
                  }`}
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {conversation.clientName.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {conversation.clientName}
                        </p>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(conversation.lastMessageTime)}
                          </p>
                          {conversation.unreadCount > 0 && (
                            <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
                              {conversation.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                      {conversation.serviceName && (
                        <p className="text-xs text-primary-600 dark:text-primary-400 truncate">
                          {conversation.serviceName}
                        </p>
                      )}
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {conversation.lastMessage}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">
                    {selectedConversation.clientName.charAt(0)}
                  </span>
                </div>
                <div>
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    {selectedConversation.clientName}
                  </h2>
                  {selectedConversation.serviceName && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {selectedConversation.serviceName}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
              {selectedConversation.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${
                    message.senderId === 'expert1' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.senderId === 'expert1'
                        ? 'bg-primary-600 text-white'
                        : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    <p
                      className={`text-xs mt-1 ${
                        message.senderId === 'expert1'
                          ? 'text-primary-100'
                          : 'text-gray-500 dark:text-gray-400'
                      }`}
                    >
                      {formatTime(message.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
              <div className="flex space-x-3 rtl:space-x-reverse">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="اكتب رسالتك..."
                  className="flex-1 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <button
                  type="button"
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim() || isLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  <PaperAirplaneIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <EmptyState
              icon={<ChatBubbleLeftIcon className="h-12 w-12" />}
              title="اختر محادثة"
              description="اختر محادثة من القائمة لبدء المراسلة"
            />
          </div>
        )}
      </div>
    </div>
  );
}

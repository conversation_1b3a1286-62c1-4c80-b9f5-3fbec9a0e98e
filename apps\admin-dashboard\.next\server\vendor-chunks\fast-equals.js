"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-equals";
exports.ids = ["vendor-chunks/fast-equals"];
exports.modules = {

/***/ "(ssr)/../../node_modules/fast-equals/dist/esm/index.mjs":
/*!*********************************************************!*\
  !*** ../../node_modules/fast-equals/dist/esm/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circularDeepEqual: () => (/* binding */ circularDeepEqual),\n/* harmony export */   circularShallowEqual: () => (/* binding */ circularShallowEqual),\n/* harmony export */   createCustomEqual: () => (/* binding */ createCustomEqual),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual),\n/* harmony export */   sameValueZeroEqual: () => (/* binding */ sameValueZeroEqual),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   strictCircularDeepEqual: () => (/* binding */ strictCircularDeepEqual),\n/* harmony export */   strictCircularShallowEqual: () => (/* binding */ strictCircularShallowEqual),\n/* harmony export */   strictDeepEqual: () => (/* binding */ strictDeepEqual),\n/* harmony export */   strictShallowEqual: () => (/* binding */ strictShallowEqual)\n/* harmony export */ });\nvar getOwnPropertyNames = Object.getOwnPropertyNames, getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Combine two comparators into a single comparators.\n */ function combineComparators(comparatorA, comparatorB) {\n    return function isEqual(a, b, state) {\n        return comparatorA(a, b, state) && comparatorB(a, b, state);\n    };\n}\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */ function createIsCircular(areItemsEqual) {\n    return function isCircular(a, b, state) {\n        if (!a || !b || typeof a !== \"object\" || typeof b !== \"object\") {\n            return areItemsEqual(a, b, state);\n        }\n        var cache = state.cache;\n        var cachedA = cache.get(a);\n        var cachedB = cache.get(b);\n        if (cachedA && cachedB) {\n            return cachedA === b && cachedB === a;\n        }\n        cache.set(a, b);\n        cache.set(b, a);\n        var result = areItemsEqual(a, b, state);\n        cache.delete(a);\n        cache.delete(b);\n        return result;\n    };\n}\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */ function getStrictProperties(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Whether the object contains the property passed as an own property.\n */ var hasOwn = Object.hasOwn || function(object, property) {\n    return hasOwnProperty.call(object, property);\n};\n/**\n * Whether the values passed are strictly equal or both NaN.\n */ function sameValueZeroEqual(a, b) {\n    return a === b || !a && !b && a !== a && b !== b;\n}\nvar PREACT_VNODE = \"__v\";\nvar PREACT_OWNER = \"__o\";\nvar REACT_OWNER = \"_owner\";\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, keys = Object.keys;\n/**\n * Whether the arrays are equal in value.\n */ function areArraysEqual(a, b, state) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while(index-- > 0){\n        if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the dates passed are equal in value.\n */ function areDatesEqual(a, b) {\n    return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n/**\n * Whether the errors passed are equal in value.\n */ function areErrorsEqual(a, b) {\n    return a.name === b.name && a.message === b.message && a.cause === b.cause && a.stack === b.stack;\n}\n/**\n * Whether the functions passed are equal in value.\n */ function areFunctionsEqual(a, b) {\n    return a === b;\n}\n/**\n * Whether the `Map`s are equal in value.\n */ function areMapsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n        return false;\n    }\n    if (!size) {\n        return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.entries();\n    var aResult;\n    var bResult;\n    var index = 0;\n    while(aResult = aIterable.next()){\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.entries();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while(bResult = bIterable.next()){\n            if (bResult.done) {\n                break;\n            }\n            if (matchedIndices[matchIndex]) {\n                matchIndex++;\n                continue;\n            }\n            var aEntry = aResult.value;\n            var bEntry = bResult.value;\n            if (state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) && state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)) {\n                hasMatch = matchedIndices[matchIndex] = true;\n                break;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n        index++;\n    }\n    return true;\n}\n/**\n * Whether the numbers are equal in value.\n */ var areNumbersEqual = sameValueZeroEqual;\n/**\n * Whether the objects are equal in value.\n */ function areObjectsEqual(a, b, state) {\n    var properties = keys(a);\n    var index = properties.length;\n    if (keys(b).length !== index) {\n        return false;\n    }\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while(index-- > 0){\n        if (!isPropertyEqual(a, b, state, properties[index])) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value with strict property checking.\n */ function areObjectsEqualStrict(a, b, state) {\n    var properties = getStrictProperties(a);\n    var index = properties.length;\n    if (getStrictProperties(b).length !== index) {\n        return false;\n    }\n    var property;\n    var descriptorA;\n    var descriptorB;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while(index-- > 0){\n        property = properties[index];\n        if (!isPropertyEqual(a, b, state, property)) {\n            return false;\n        }\n        descriptorA = getOwnPropertyDescriptor(a, property);\n        descriptorB = getOwnPropertyDescriptor(b, property);\n        if ((descriptorA || descriptorB) && (!descriptorA || !descriptorB || descriptorA.configurable !== descriptorB.configurable || descriptorA.enumerable !== descriptorB.enumerable || descriptorA.writable !== descriptorB.writable)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the primitive wrappers passed are equal in value.\n */ function arePrimitiveWrappersEqual(a, b) {\n    return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n/**\n * Whether the regexps passed are equal in value.\n */ function areRegExpsEqual(a, b) {\n    return a.source === b.source && a.flags === b.flags;\n}\n/**\n * Whether the `Set`s are equal in value.\n */ function areSetsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n        return false;\n    }\n    if (!size) {\n        return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.values();\n    var aResult;\n    var bResult;\n    while(aResult = aIterable.next()){\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.values();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while(bResult = bIterable.next()){\n            if (bResult.done) {\n                break;\n            }\n            if (!matchedIndices[matchIndex] && state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state)) {\n                hasMatch = matchedIndices[matchIndex] = true;\n                break;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the TypedArray instances are equal in value.\n */ function areTypedArraysEqual(a, b) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while(index-- > 0){\n        if (a[index] !== b[index]) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the URL instances are equal in value.\n */ function areUrlsEqual(a, b) {\n    return a.hostname === b.hostname && a.pathname === b.pathname && a.protocol === b.protocol && a.port === b.port && a.hash === b.hash && a.username === b.username && a.password === b.password;\n}\nfunction isPropertyEqual(a, b, state, property) {\n    if ((property === REACT_OWNER || property === PREACT_OWNER || property === PREACT_VNODE) && (a.$$typeof || b.$$typeof)) {\n        return true;\n    }\n    return hasOwn(b, property) && state.equals(a[property], b[property], property, property, a, b, state);\n}\nvar ARGUMENTS_TAG = \"[object Arguments]\";\nvar BOOLEAN_TAG = \"[object Boolean]\";\nvar DATE_TAG = \"[object Date]\";\nvar ERROR_TAG = \"[object Error]\";\nvar MAP_TAG = \"[object Map]\";\nvar NUMBER_TAG = \"[object Number]\";\nvar OBJECT_TAG = \"[object Object]\";\nvar REG_EXP_TAG = \"[object RegExp]\";\nvar SET_TAG = \"[object Set]\";\nvar STRING_TAG = \"[object String]\";\nvar URL_TAG = \"[object URL]\";\nvar isArray = Array.isArray;\nvar isTypedArray = typeof ArrayBuffer === \"function\" && ArrayBuffer.isView ? ArrayBuffer.isView : null;\nvar assign = Object.assign;\nvar getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */ function createEqualityComparator(_a) {\n    var areArraysEqual = _a.areArraysEqual, areDatesEqual = _a.areDatesEqual, areErrorsEqual = _a.areErrorsEqual, areFunctionsEqual = _a.areFunctionsEqual, areMapsEqual = _a.areMapsEqual, areNumbersEqual = _a.areNumbersEqual, areObjectsEqual = _a.areObjectsEqual, arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual, areRegExpsEqual = _a.areRegExpsEqual, areSetsEqual = _a.areSetsEqual, areTypedArraysEqual = _a.areTypedArraysEqual, areUrlsEqual = _a.areUrlsEqual;\n    /**\n     * compare the value of the two objects and return true if they are equivalent in values\n     */ return function comparator(a, b, state) {\n        // If the items are strictly equal, no need to do a value comparison.\n        if (a === b) {\n            return true;\n        }\n        // If either of the items are nullish and fail the strictly equal check\n        // above, then they must be unequal.\n        if (a == null || b == null) {\n            return false;\n        }\n        var type = typeof a;\n        if (type !== typeof b) {\n            return false;\n        }\n        if (type !== \"object\") {\n            if (type === \"number\") {\n                return areNumbersEqual(a, b, state);\n            }\n            if (type === \"function\") {\n                return areFunctionsEqual(a, b, state);\n            }\n            // If a primitive value that is not strictly equal, it must be unequal.\n            return false;\n        }\n        var constructor = a.constructor;\n        // Checks are listed in order of commonality of use-case:\n        //   1. Common complex object types (plain object, array)\n        //   2. Common data values (date, regexp)\n        //   3. Less-common complex object types (map, set)\n        //   4. Less-common data values (promise, primitive wrappers)\n        // Inherently this is both subjective and assumptive, however\n        // when reviewing comparable libraries in the wild this order\n        // appears to be generally consistent.\n        // Constructors should match, otherwise there is potential for false positives\n        // between class and subclass or custom object and POJO.\n        if (constructor !== b.constructor) {\n            return false;\n        }\n        // `isPlainObject` only checks against the object's own realm. Cross-realm\n        // comparisons are rare, and will be handled in the ultimate fallback, so\n        // we can avoid capturing the string tag.\n        if (constructor === Object) {\n            return areObjectsEqual(a, b, state);\n        }\n        // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n        // the string tag or doing an `instanceof` check.\n        if (isArray(a)) {\n            return areArraysEqual(a, b, state);\n        }\n        // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n        // capturing the string tag or comparing against all possible constructors.\n        if (isTypedArray != null && isTypedArray(a)) {\n            return areTypedArraysEqual(a, b, state);\n        }\n        // Try to fast-path equality checks for other complex object types in the\n        // same realm to avoid capturing the string tag. Strict equality is used\n        // instead of `instanceof` because it is more performant for the common\n        // use-case. If someone is subclassing a native class, it will be handled\n        // with the string tag comparison.\n        if (constructor === Date) {\n            return areDatesEqual(a, b, state);\n        }\n        if (constructor === RegExp) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (constructor === Map) {\n            return areMapsEqual(a, b, state);\n        }\n        if (constructor === Set) {\n            return areSetsEqual(a, b, state);\n        }\n        // Since this is a custom object, capture the string tag to determing its type.\n        // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n        var tag = getTag(a);\n        if (tag === DATE_TAG) {\n            return areDatesEqual(a, b, state);\n        }\n        // For RegExp, the properties are not enumerable, and therefore will give false positives if\n        // tested like a standard object.\n        if (tag === REG_EXP_TAG) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (tag === MAP_TAG) {\n            return areMapsEqual(a, b, state);\n        }\n        if (tag === SET_TAG) {\n            return areSetsEqual(a, b, state);\n        }\n        if (tag === OBJECT_TAG) {\n            // The exception for value comparison is custom `Promise`-like class instances. These should\n            // be treated the same as standard `Promise` objects, which means strict equality, and if\n            // it reaches this point then that strict equality comparison has already failed.\n            return typeof a.then !== \"function\" && typeof b.then !== \"function\" && areObjectsEqual(a, b, state);\n        }\n        // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n        // enumerable, and therefore will give false positives if tested like a standard object.\n        if (tag === URL_TAG) {\n            return areUrlsEqual(a, b, state);\n        }\n        // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n        // enumerable, and therefore will give false positives if tested like a standard object.\n        if (tag === ERROR_TAG) {\n            return areErrorsEqual(a, b, state);\n        }\n        // If an arguments tag, it should be treated as a standard object.\n        if (tag === ARGUMENTS_TAG) {\n            return areObjectsEqual(a, b, state);\n        }\n        // As the penultimate fallback, check if the values passed are primitive wrappers. This\n        // is very rare in modern JS, which is why it is deprioritized compared to all other object\n        // types.\n        if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n            return arePrimitiveWrappersEqual(a, b, state);\n        }\n        // If not matching any tags that require a specific type of comparison, then we hard-code false because\n        // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n        //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n        //     comparison that can be made.\n        //   - For types that can be introspected, but rarely have requirements to be compared\n        //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n        //     use-cases (may be included in a future release, if requested enough).\n        //   - For types that can be introspected but do not have an objective definition of what\n        //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n        // In all cases, these decisions should be reevaluated based on changes to the language and\n        // common development practices.\n        return false;\n    };\n}\n/**\n * Create the configuration object used for building comparators.\n */ function createEqualityComparatorConfig(_a) {\n    var circular = _a.circular, createCustomConfig = _a.createCustomConfig, strict = _a.strict;\n    var config = {\n        areArraysEqual: strict ? areObjectsEqualStrict : areArraysEqual,\n        areDatesEqual: areDatesEqual,\n        areErrorsEqual: areErrorsEqual,\n        areFunctionsEqual: areFunctionsEqual,\n        areMapsEqual: strict ? combineComparators(areMapsEqual, areObjectsEqualStrict) : areMapsEqual,\n        areNumbersEqual: areNumbersEqual,\n        areObjectsEqual: strict ? areObjectsEqualStrict : areObjectsEqual,\n        arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n        areRegExpsEqual: areRegExpsEqual,\n        areSetsEqual: strict ? combineComparators(areSetsEqual, areObjectsEqualStrict) : areSetsEqual,\n        areTypedArraysEqual: strict ? areObjectsEqualStrict : areTypedArraysEqual,\n        areUrlsEqual: areUrlsEqual\n    };\n    if (createCustomConfig) {\n        config = assign({}, config, createCustomConfig(config));\n    }\n    if (circular) {\n        var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n        var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n        var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n        var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n        config = assign({}, config, {\n            areArraysEqual: areArraysEqual$1,\n            areMapsEqual: areMapsEqual$1,\n            areObjectsEqual: areObjectsEqual$1,\n            areSetsEqual: areSetsEqual$1\n        });\n    }\n    return config;\n}\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */ function createInternalEqualityComparator(compare) {\n    return function(a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n        return compare(a, b, state);\n    };\n}\n/**\n * Create the `isEqual` function used by the consuming application.\n */ function createIsEqual(_a) {\n    var circular = _a.circular, comparator = _a.comparator, createState = _a.createState, equals = _a.equals, strict = _a.strict;\n    if (createState) {\n        return function isEqual(a, b) {\n            var _a = createState(), _b = _a.cache, cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b, meta = _a.meta;\n            return comparator(a, b, {\n                cache: cache,\n                equals: equals,\n                meta: meta,\n                strict: strict\n            });\n        };\n    }\n    if (circular) {\n        return function isEqual(a, b) {\n            return comparator(a, b, {\n                cache: new WeakMap(),\n                equals: equals,\n                meta: undefined,\n                strict: strict\n            });\n        };\n    }\n    var state = {\n        cache: undefined,\n        equals: equals,\n        meta: undefined,\n        strict: strict\n    };\n    return function isEqual(a, b) {\n        return comparator(a, b, state);\n    };\n}\n/**\n * Whether the items passed are deeply-equal in value.\n */ var deepEqual = createCustomEqual();\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */ var strictDeepEqual = createCustomEqual({\n    strict: true\n});\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */ var circularDeepEqual = createCustomEqual({\n    circular: true\n});\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */ var strictCircularDeepEqual = createCustomEqual({\n    circular: true,\n    strict: true\n});\n/**\n * Whether the items passed are shallowly-equal in value.\n */ var shallowEqual = createCustomEqual({\n    createInternalComparator: function() {\n        return sameValueZeroEqual;\n    }\n});\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */ var strictShallowEqual = createCustomEqual({\n    strict: true,\n    createInternalComparator: function() {\n        return sameValueZeroEqual;\n    }\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */ var circularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function() {\n        return sameValueZeroEqual;\n    }\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */ var strictCircularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function() {\n        return sameValueZeroEqual;\n    },\n    strict: true\n});\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */ function createCustomEqual(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    var _a = options.circular, circular = _a === void 0 ? false : _a, createCustomInternalComparator = options.createInternalComparator, createState = options.createState, _b = options.strict, strict = _b === void 0 ? false : _b;\n    var config = createEqualityComparatorConfig(options);\n    var comparator = createEqualityComparator(config);\n    var equals = createCustomInternalComparator ? createCustomInternalComparator(comparator) : createInternalEqualityComparator(comparator);\n    return createIsEqual({\n        circular: circular,\n        comparator: comparator,\n        createState: createState,\n        equals: equals,\n        strict: strict\n    });\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fast-equals/dist/esm/index.mjs\n");

/***/ })

};
;
'use client';

import { useState } from 'react';
import { 
  CurrencyDollarIcon,
  ArrowDownTrayIcon,
  ChartBarIcon,
  CalendarIcon,
  BanknotesIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingSpinner';

interface EarningsData {
  totalEarnings: number;
  availableBalance: number;
  pendingPayments: number;
  totalWithdrawn: number;
  thisMonthEarnings: number;
  lastMonthEarnings: number;
}

interface Transaction {
  id: string;
  type: 'earning' | 'withdrawal' | 'refund';
  amount: number;
  description: string;
  date: string;
  status: 'completed' | 'pending' | 'failed';
  bookingId?: string;
}

const mockEarnings: EarningsData = {
  totalEarnings: 2450.00,
  availableBalance: 1200.50,
  pendingPayments: 350.00,
  totalWithdrawn: 900.00,
  thisMonthEarnings: 650.00,
  lastMonthEarnings: 580.00,
};

const mockTransactions: Transaction[] = [
  {
    id: '1',
    type: 'earning',
    amount: 120.00,
    description: 'استشارة تطوير موقع إلكتروني - سارة أحمد',
    date: '2024-01-22',
    status: 'completed',
    bookingId: 'BK001',
  },
  {
    id: '2',
    type: 'withdrawal',
    amount: -200.00,
    description: 'سحب إلى الحساب البنكي',
    date: '2024-01-20',
    status: 'completed',
  },
  {
    id: '3',
    type: 'earning',
    amount: 85.00,
    description: 'مراجعة كود البرمجة - محمد علي',
    date: '2024-01-18',
    status: 'pending',
    bookingId: 'BK002',
  },
  {
    id: '4',
    type: 'earning',
    amount: 150.00,
    description: 'تدريب على React Native - نور حسن',
    date: '2024-01-15',
    status: 'completed',
    bookingId: 'BK003',
  },
];

export default function EarningsPage() {
  const [earnings] = useState<EarningsData>(mockEarnings);
  const [transactions] = useState<Transaction[]>(mockTransactions);
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [isLoading, setIsLoading] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'earning':
        return <CurrencyDollarIcon className="h-5 w-5 text-green-500" />;
      case 'withdrawal':
        return <ArrowDownTrayIcon className="h-5 w-5 text-blue-500" />;
      case 'refund':
        return <BanknotesIcon className="h-5 w-5 text-red-500" />;
      default:
        return <CurrencyDollarIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    };
    
    const labels = {
      completed: 'مكتمل',
      pending: 'في الانتظار',
      failed: 'فشل',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  const handleWithdraw = async () => {
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    if (parseFloat(withdrawAmount) > earnings.availableBalance) {
      alert('المبلغ المطلوب أكبر من الرصيد المتاح');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('تم إرسال طلب السحب بنجاح. سيتم معالجته خلال 1-3 أيام عمل.');
      setShowWithdrawModal(false);
      setWithdrawAmount('');
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      alert('حدث خطأ أثناء معالجة طلب السحب');
    } finally {
      setIsLoading(false);
    }
  };

  const growthPercentage = earnings.lastMonthEarnings > 0 
    ? ((earnings.thisMonthEarnings - earnings.lastMonthEarnings) / earnings.lastMonthEarnings * 100)
    : 0;

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          الأرباح والمدفوعات
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          تتبع أرباحك ومدفوعاتك وقم بسحب أموالك
        </p>
      </div>

      {/* Earnings Overview */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الأرباح
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${earnings.totalEarnings.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BanknotesIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    الرصيد المتاح
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${earnings.availableBalance.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    مدفوعات معلقة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${earnings.pendingPayments.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowDownTrayIcon className="h-6 w-6 text-purple-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي المسحوب
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${earnings.totalWithdrawn.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Monthly Performance */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            الأداء الشهري
          </h3>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="this_month">هذا الشهر</option>
            <option value="last_month">الشهر الماضي</option>
            <option value="last_3_months">آخر 3 أشهر</option>
          </select>
        </div>
        
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-primary-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  أرباح هذا الشهر
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${earnings.thisMonthEarnings.toFixed(2)}
                </p>
                <p className={`text-sm ${growthPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {growthPercentage >= 0 ? '+' : ''}{growthPercentage.toFixed(1)}% من الشهر الماضي
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-gray-500" />
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  أرباح الشهر الماضي
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${earnings.lastMonthEarnings.toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Withdraw Section */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              سحب الأموال
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              الرصيد المتاح للسحب: ${earnings.availableBalance.toFixed(2)}
            </p>
          </div>
          <button
            type="button"
            onClick={() => setShowWithdrawModal(true)}
            disabled={earnings.availableBalance <= 0}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowDownTrayIcon className="h-4 w-4 ml-2" />
            طلب سحب
          </button>
        </div>
      </div>

      {/* Withdraw Modal */}
      {showWithdrawModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              طلب سحب الأموال
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  المبلغ المطلوب سحبه ($)
                </label>
                <input
                  type="number"
                  min="10"
                  max={earnings.availableBalance}
                  step="0.01"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="أدخل المبلغ"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  الحد الأدنى للسحب: $10
                </p>
              </div>
              
              <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-3">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  سيتم معالجة طلب السحب خلال 1-3 أيام عمل. سيتم خصم رسوم معالجة قدرها $2.
                </p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <button
                type="button"
                onClick={() => setShowWithdrawModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                إلغاء
              </button>
              <button
                type="button"
                onClick={handleWithdraw}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {isLoading ? 'جاري المعالجة...' : 'تأكيد السحب'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Transaction History */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            سجل المعاملات
          </h3>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {transactions.map((transaction) => (
            <div key={transaction.id} className="px-6 py-4 flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                {getTransactionIcon(transaction.type)}
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {transaction.description}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {transaction.date}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <span className={`text-sm font-medium ${
                  transaction.amount > 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount).toFixed(2)}
                </span>
                {getStatusBadge(transaction.status)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

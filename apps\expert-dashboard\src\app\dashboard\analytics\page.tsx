'use client';

import { useState } from 'react';
import { 
  ChartBarIcon,
  TrendingUpIcon,
  UserGroupIcon,
  StarIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  EyeIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  overview: {
    totalBookings: number;
    completedBookings: number;
    totalEarnings: number;
    averageRating: number;
    profileViews: number;
    responseTime: number; // in minutes
  };
  monthlyStats: {
    month: string;
    bookings: number;
    earnings: number;
    rating: number;
  }[];
  servicePerformance: {
    serviceName: string;
    bookings: number;
    earnings: number;
    rating: number;
    views: number;
  }[];
  clientFeedback: {
    id: string;
    clientName: string;
    serviceName: string;
    rating: number;
    comment: string;
    date: string;
  }[];
}

const mockAnalytics: AnalyticsData = {
  overview: {
    totalBookings: 45,
    completedBookings: 38,
    totalEarnings: 2450.00,
    averageRating: 4.8,
    profileViews: 156,
    responseTime: 15,
  },
  monthlyStats: [
    { month: 'أكتوبر', bookings: 12, earnings: 850, rating: 4.7 },
    { month: 'نوفمبر', bookings: 15, earnings: 980, rating: 4.8 },
    { month: 'ديسمبر', bookings: 18, earnings: 1200, rating: 4.9 },
  ],
  servicePerformance: [
    {
      serviceName: 'استشارة تطوير موقع إلكتروني',
      bookings: 15,
      earnings: 1125,
      rating: 4.9,
      views: 89,
    },
    {
      serviceName: 'مراجعة كود البرمجة',
      bookings: 12,
      earnings: 720,
      rating: 4.8,
      views: 67,
    },
    {
      serviceName: 'تدريب على React Native',
      bookings: 8,
      earnings: 600,
      rating: 4.7,
      views: 45,
    },
  ],
  clientFeedback: [
    {
      id: '1',
      clientName: 'سارة أحمد',
      serviceName: 'استشارة تطوير موقع إلكتروني',
      rating: 5,
      comment: 'خبير ممتاز ومفيد جداً. ساعدني كثيراً في فهم متطلبات المشروع.',
      date: '2024-01-20',
    },
    {
      id: '2',
      clientName: 'محمد علي',
      serviceName: 'مراجعة كود البرمجة',
      rating: 5,
      comment: 'مراجعة شاملة ومفصلة. اكتشف مشاكل لم ألاحظها.',
      date: '2024-01-18',
    },
    {
      id: '3',
      clientName: 'نور حسن',
      serviceName: 'تدريب على React Native',
      rating: 4,
      comment: 'تدريب جيد ومفيد، أتمنى لو كان هناك المزيد من الأمثلة العملية.',
      date: '2024-01-15',
    },
  ],
};

export default function ExpertAnalyticsPage() {
  const [analytics] = useState<AnalyticsData>(mockAnalytics);
  const [selectedPeriod, setSelectedPeriod] = useState('last_3_months');

  const completionRate = analytics.overview.totalBookings > 0 
    ? (analytics.overview.completedBookings / analytics.overview.totalBookings * 100)
    : 0;

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          تحليلات الأداء
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          تتبع أداءك وإحصائياتك كخبير في منصة فريلا سوريا
        </p>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الحجوزات
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {analytics.overview.totalBookings}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUpIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    معدل الإكمال
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {completionRate.toFixed(1)}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <StarIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    متوسط التقييم
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {analytics.overview.averageRating.toFixed(1)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <EyeIcon className="h-6 w-6 text-purple-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    مشاهدات الملف الشخصي
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {analytics.overview.profileViews}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الأرباح
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${analytics.overview.totalEarnings.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    متوسط وقت الاستجابة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {analytics.overview.responseTime} دقيقة
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Monthly Performance */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            الأداء الشهري
          </h3>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="last_3_months">آخر 3 أشهر</option>
            <option value="last_6_months">آخر 6 أشهر</option>
            <option value="last_year">آخر سنة</option>
          </select>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الشهر
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الحجوزات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الأرباح
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  التقييم
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {analytics.monthlyStats.map((stat, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {stat.month}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {stat.bookings}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ${stat.earnings}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900 dark:text-white ml-2">
                        {stat.rating.toFixed(1)}
                      </span>
                      <div className="flex">
                        {renderStars(stat.rating)}
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Service Performance */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          أداء الخدمات
        </h3>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الخدمة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الحجوزات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الأرباح
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  التقييم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المشاهدات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {analytics.servicePerformance.map((service, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {service.serviceName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {service.bookings}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ${service.earnings}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900 dark:text-white ml-2">
                        {service.rating.toFixed(1)}
                      </span>
                      <div className="flex">
                        {renderStars(service.rating)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {service.views}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Client Feedback */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          آراء العملاء الأخيرة
        </h3>
        
        <div className="space-y-4">
          {analytics.clientFeedback.map((feedback) => (
            <div key={feedback.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {feedback.clientName}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    - {feedback.serviceName}
                  </span>
                </div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <div className="flex">
                    {renderStars(feedback.rating)}
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {feedback.date}
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                {feedback.comment}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { 
  CalendarIcon,
  ClockIcon,
  UserIcon,
  CheckIcon,
  XMarkIcon,
  ChatBubbleLeftIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { LoadingState } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';

interface Booking {
  id: string;
  clientName: string;
  clientAvatar?: string;
  serviceName: string;
  date: string;
  time: string;
  duration: number;
  amount: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'refunded';
  notes?: string;
  createdAt: string;
}

const mockBookings: Booking[] = [
  {
    id: '1',
    clientName: 'سارة أحمد',
    serviceName: 'استشارة تطوير موقع إلكتروني',
    date: '2024-01-25',
    time: '14:00',
    duration: 60,
    amount: 75,
    status: 'confirmed',
    paymentStatus: 'paid',
    notes: 'العميل يريد مناقشة متطلبات الموقع الإلكتروني للشركة',
    createdAt: '2024-01-20',
  },
  {
    id: '2',
    clientName: 'محمد علي',
    serviceName: 'مراجعة كود البرمجة',
    date: '2024-01-26',
    time: '10:00',
    duration: 90,
    amount: 120,
    status: 'pending',
    paymentStatus: 'pending',
    notes: 'مراجعة كود React.js وتحسين الأداء',
    createdAt: '2024-01-22',
  },
  {
    id: '3',
    clientName: 'نور حسن',
    serviceName: 'تدريب على React Native',
    date: '2024-01-28',
    time: '16:00',
    duration: 120,
    amount: 150,
    status: 'confirmed',
    paymentStatus: 'paid',
    createdAt: '2024-01-18',
  },
];

export default function ExpertBookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>(mockBookings);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);

  const filteredBookings = bookings.filter(booking => {
    return selectedStatus === 'all' || booking.status === selectedStatus;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      confirmed: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    };
    
    const labels = {
      pending: 'في الانتظار',
      confirmed: 'مؤكد',
      completed: 'مكتمل',
      cancelled: 'ملغي',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  const handleAcceptBooking = async (bookingId: string) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setBookings(prev => prev.map(booking => 
        booking.id === bookingId ? { ...booking, status: 'confirmed' as const } : booking
      ));
    } catch (error) {
      console.error('Error accepting booking:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectBooking = async (bookingId: string) => {
    if (window.confirm('هل أنت متأكد من رفض هذا الحجز؟')) {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setBookings(prev => prev.map(booking => 
          booking.id === bookingId ? { ...booking, status: 'cancelled' as const } : booking
        ));
      } catch (error) {
        console.error('Error rejecting booking:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCompleteBooking = async (bookingId: string) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setBookings(prev => prev.map(booking => 
        booking.id === bookingId ? { ...booking, status: 'completed' as const } : booking
      ));
    } catch (error) {
      console.error('Error completing booking:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const upcomingBookings = bookings.filter(b => b.status === 'confirmed').length;
  const pendingBookings = bookings.filter(b => b.status === 'pending').length;
  const completedBookings = bookings.filter(b => b.status === 'completed').length;
  const totalEarnings = bookings
    .filter(b => b.status === 'completed')
    .reduce((sum, b) => sum + b.amount, 0);

  if (isLoading) {
    return <LoadingState message="جاري تحديث الحجز..." />;
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          إدارة الحجوزات
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          قم بإدارة حجوزاتك ومواعيدك مع العملاء
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    المواعيد القادمة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {upcomingBookings}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    في الانتظار
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {pendingBookings}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    مكتملة
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {completedBookings}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserIcon className="h-6 w-6 text-primary-400" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    إجمالي الأرباح
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${totalEarnings}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filter */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            تصفية حسب الحالة:
          </label>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="all">جميع الحجوزات</option>
            <option value="pending">في الانتظار</option>
            <option value="confirmed">مؤكدة</option>
            <option value="completed">مكتملة</option>
            <option value="cancelled">ملغية</option>
          </select>
        </div>
      </div>

      {/* Bookings List */}
      {filteredBookings.length === 0 ? (
        <EmptyState
          icon={<CalendarIcon className="h-12 w-12" />}
          title="لا توجد حجوزات"
          description="لم يتم العثور على حجوزات تطابق المعايير المحددة"
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredBookings.map((booking) => (
              <li key={booking.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {booking.clientName.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {booking.serviceName}
                        </p>
                        {getStatusBadge(booking.status)}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        العميل: {booking.clientName}
                      </p>
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <CalendarIcon className="h-4 w-4 ml-1" />
                          {booking.date}
                        </span>
                        <span className="flex items-center">
                          <ClockIcon className="h-4 w-4 ml-1" />
                          {booking.time} ({booking.duration} دقيقة)
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          ${booking.amount}
                        </span>
                      </div>
                      {booking.notes && (
                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                          {booking.notes}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button
                      type="button"
                      className="text-primary-600 hover:text-primary-900 dark:text-primary-400"
                      title="عرض التفاصيل"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    
                    <button
                      type="button"
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400"
                      title="إرسال رسالة"
                    >
                      <ChatBubbleLeftIcon className="h-5 w-5" />
                    </button>
                    
                    {booking.status === 'pending' && (
                      <>
                        <button
                          type="button"
                          onClick={() => handleAcceptBooking(booking.id)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400"
                          title="قبول الحجز"
                        >
                          <CheckIcon className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRejectBooking(booking.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400"
                          title="رفض الحجز"
                        >
                          <XMarkIcon className="h-5 w-5" />
                        </button>
                      </>
                    )}
                    
                    {booking.status === 'confirmed' && (
                      <button
                        type="button"
                        onClick={() => handleCompleteBooking(booking.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-green-600 hover:bg-green-700"
                      >
                        إكمال الجلسة
                      </button>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

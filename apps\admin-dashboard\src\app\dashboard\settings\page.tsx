'use client';

import { useState } from 'react';
import { 
  CogIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  BellIcon,
  GlobeAltIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface SystemSettings {
  platform: {
    name: string;
    description: string;
    supportEmail: string;
    supportPhone: string;
  };
  fees: {
    platformFeePercentage: number;
    minimumBookingAmount: number;
    maximumBookingAmount: number;
    withdrawalFee: number;
    minimumWithdrawal: number;
  };
  security: {
    requireEmailVerification: boolean;
    requirePhoneVerification: boolean;
    enableTwoFactorAuth: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    marketingEmails: boolean;
  };
  localization: {
    defaultLanguage: string;
    supportedLanguages: string[];
    defaultCurrency: string;
    timezone: string;
  };
  content: {
    termsOfService: string;
    privacyPolicy: string;
    aboutUs: string;
    contactInfo: string;
  };
}

const defaultSettings: SystemSettings = {
  platform: {
    name: 'فريلا سوريا',
    description: 'منصة الخدمات المهنية الرائدة في سوريا',
    supportEmail: '<EMAIL>',
    supportPhone: '+963-11-1234567',
  },
  fees: {
    platformFeePercentage: 10,
    minimumBookingAmount: 25,
    maximumBookingAmount: 5000,
    withdrawalFee: 2,
    minimumWithdrawal: 50,
  },
  security: {
    requireEmailVerification: true,
    requirePhoneVerification: false,
    enableTwoFactorAuth: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    marketingEmails: false,
  },
  localization: {
    defaultLanguage: 'ar',
    supportedLanguages: ['ar', 'en'],
    defaultCurrency: 'USD',
    timezone: 'Asia/Damascus',
  },
  content: {
    termsOfService: 'شروط الخدمة...',
    privacyPolicy: 'سياسة الخصوصية...',
    aboutUs: 'نبذة عنا...',
    contactInfo: 'معلومات التواصل...',
  },
};

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('platform');

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Settings saved successfully
      alert('تم حفظ الإعدادات بنجاح');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = (section: keyof SystemSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const tabs = [
    { id: 'platform', name: 'إعدادات المنصة', icon: CogIcon },
    { id: 'fees', name: 'الرسوم والمدفوعات', icon: CurrencyDollarIcon },
    { id: 'security', name: 'الأمان', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'الإشعارات', icon: BellIcon },
    { id: 'localization', name: 'اللغة والمنطقة', icon: GlobeAltIcon },
    { id: 'content', name: 'المحتوى', icon: DocumentTextIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          إعدادات النظام
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          إدارة إعدادات منصة فريلا سوريا والتحكم في خصائص النظام
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8 rtl:space-x-reverse px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <Icon className="h-5 w-5 ml-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
          {/* Platform Settings */}
          {activeTab === 'platform' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                إعدادات المنصة الأساسية
              </h3>
              
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    اسم المنصة
                  </label>
                  <input
                    type="text"
                    value={settings.platform.name}
                    onChange={(e) => updateSettings('platform', 'name', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    بريد الدعم الفني
                  </label>
                  <input
                    type="email"
                    value={settings.platform.supportEmail}
                    onChange={(e) => updateSettings('platform', 'supportEmail', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    هاتف الدعم الفني
                  </label>
                  <input
                    type="tel"
                    value={settings.platform.supportPhone}
                    onChange={(e) => updateSettings('platform', 'supportPhone', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  وصف المنصة
                </label>
                <textarea
                  rows={3}
                  value={settings.platform.description}
                  onChange={(e) => updateSettings('platform', 'description', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          )}

          {/* Fees Settings */}
          {activeTab === 'fees' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                إعدادات الرسوم والمدفوعات
              </h3>
              
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    نسبة رسوم المنصة (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="50"
                    step="0.1"
                    value={settings.fees.platformFeePercentage}
                    onChange={(e) => updateSettings('fees', 'platformFeePercentage', parseFloat(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الحد الأدنى للحجز ($)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={settings.fees.minimumBookingAmount}
                    onChange={(e) => updateSettings('fees', 'minimumBookingAmount', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الحد الأقصى للحجز ($)
                  </label>
                  <input
                    type="number"
                    min="100"
                    value={settings.fees.maximumBookingAmount}
                    onChange={(e) => updateSettings('fees', 'maximumBookingAmount', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    رسوم السحب ($)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.1"
                    value={settings.fees.withdrawalFee}
                    onChange={(e) => updateSettings('fees', 'withdrawalFee', parseFloat(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الحد الأدنى للسحب ($)
                  </label>
                  <input
                    type="number"
                    min="10"
                    value={settings.fees.minimumWithdrawal}
                    onChange={(e) => updateSettings('fees', 'minimumWithdrawal', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Security Settings */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                إعدادات الأمان
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      تأكيد البريد الإلكتروني مطلوب
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      يجب على المستخدمين تأكيد بريدهم الإلكتروني قبل استخدام المنصة
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.security.requireEmailVerification}
                    onChange={(e) => updateSettings('security', 'requireEmailVerification', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      تأكيد رقم الهاتف مطلوب
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      يجب على المستخدمين تأكيد رقم هاتفهم
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.security.requirePhoneVerification}
                    onChange={(e) => updateSettings('security', 'requirePhoneVerification', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      تفعيل المصادقة الثنائية
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      تمكين المصادقة الثنائية للحسابات الحساسة
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.security.enableTwoFactorAuth}
                    onChange={(e) => updateSettings('security', 'enableTwoFactorAuth', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    مهلة انتهاء الجلسة (دقيقة)
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="1440"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    الحد الأقصى لمحاولات تسجيل الدخول
                  </label>
                  <input
                    type="number"
                    min="3"
                    max="10"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => updateSettings('security', 'maxLoginAttempts', parseInt(e.target.value))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Notifications Settings */}
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                إعدادات الإشعارات
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      إشعارات البريد الإلكتروني
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      إرسال إشعارات مهمة عبر البريد الإلكتروني
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.emailNotifications}
                    onChange={(e) => updateSettings('notifications', 'emailNotifications', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      إشعارات الرسائل النصية
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      إرسال إشعارات عاجلة عبر الرسائل النصية
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.smsNotifications}
                    onChange={(e) => updateSettings('notifications', 'smsNotifications', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      الإشعارات الفورية
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      إرسال إشعارات فورية للتطبيق المحمول
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.pushNotifications}
                    onChange={(e) => updateSettings('notifications', 'pushNotifications', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      رسائل تسويقية
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      إرسال رسائل تسويقية وعروض ترويجية
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.marketingEmails}
                    onChange={(e) => updateSettings('notifications', 'marketingEmails', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Localization Settings */}
          {activeTab === 'localization' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                إعدادات اللغة والمنطقة
              </h3>
              
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    اللغة الافتراضية
                  </label>
                  <select
                    value={settings.localization.defaultLanguage}
                    onChange={(e) => updateSettings('localization', 'defaultLanguage', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    العملة الافتراضية
                  </label>
                  <select
                    value={settings.localization.defaultCurrency}
                    onChange={(e) => updateSettings('localization', 'defaultCurrency', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="USD">دولار أمريكي (USD)</option>
                    <option value="SYP">ليرة سورية (SYP)</option>
                    <option value="EUR">يورو (EUR)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    المنطقة الزمنية
                  </label>
                  <select
                    value={settings.localization.timezone}
                    onChange={(e) => updateSettings('localization', 'timezone', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="Asia/Damascus">دمشق (GMT+3)</option>
                    <option value="UTC">التوقيت العالمي (UTC)</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Content Settings */}
          {activeTab === 'content' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                إعدادات المحتوى
              </h3>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    شروط الخدمة
                  </label>
                  <textarea
                    rows={4}
                    value={settings.content.termsOfService}
                    onChange={(e) => updateSettings('content', 'termsOfService', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    سياسة الخصوصية
                  </label>
                  <textarea
                    rows={4}
                    value={settings.content.privacyPolicy}
                    onChange={(e) => updateSettings('content', 'privacyPolicy', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    نبذة عنا
                  </label>
                  <textarea
                    rows={4}
                    value={settings.content.aboutUs}
                    onChange={(e) => updateSettings('content', 'aboutUs', e.target.value)}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Save button */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-b-lg">
          <div className="flex justify-end">
            <button
              type="button"
              onClick={handleSaveSettings}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading && <LoadingSpinner size="sm" color="white" className="ml-2" />}
              حفظ الإعدادات
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
